/**
 * 简化手掌翻转检测器
 * 根据训练侧别判断大拇指的位置变化
 */
import { ref } from 'vue'
import { useSimplifiedActionEngine } from './simplifiedActionEngine'

export function useSimplifiedPalmFlip() {
  const engine = useSimplifiedActionEngine()
  const { KeyPointMapping } = engine

  // 翻转检测参数
  const FLIP_THRESHOLD = 0.1  // 大拇指位置变化阈值
  const TARGET_FLIPS = 3      // 目标翻转次数

  // 翻转状态记录
  const flipState = ref({
    initialThumbX: null,
    lastThumbX: null,
    flipCount: 0,
    lastFlipTime: 0,
    direction: 0  // 1: 向右, -1: 向左, 0: 无方向
  })

  /**
   * 检测手掌翻转动作
   * @param {Array} keypoints - 归一化关键点数组 [x, y, confidence]
   * @param {string} side - 动作侧别 ('left' | 'right')
   * @returns {Object} - 检测结果 {isCompleted, progress, feedback}
   */
  const detectPalmFlip = (keypoints, side = 'left') => {
    // 确定大拇指关键点索引
    const thumbIndex = side === 'left' ? KeyPointMapping.LEFT_HAND_THUMB_4 : KeyPointMapping.RIGHT_HAND_THUMB_4
    const wristIndex = side === 'left' ? KeyPointMapping.LEFT_WRIST : KeyPointMapping.RIGHT_WRIST
    
    // 检查所需关键点是否可用
    const requiredIndices = [thumbIndex, wristIndex]
    if (!engine.checkRequiredKeypoints(keypoints, requiredIndices)) {
      return {
        isCompleted: false,
        progress: 0,
        feedback: '请保持手部在画面内'
      }
    }

    // 获取大拇指位置
    const thumb = keypoints[thumbIndex]
    const thumbCoords = engine.getCoords(thumb)
    const currentThumbX = thumbCoords.x

    // 初始化状态
    if (flipState.value.initialThumbX === null) {
      flipState.value.initialThumbX = currentThumbX
      flipState.value.lastThumbX = currentThumbX
      return {
        isCompleted: false,
        progress: 0,
        feedback: `请开始${side === 'left' ? '左手' : '右手'}翻转动作`
      }
    }

    // 检测翻转
    const positionChange = Math.abs(currentThumbX - flipState.value.lastThumbX)
    const totalChange = Math.abs(currentThumbX - flipState.value.initialThumbX)
    
    // 检测方向变化（翻转）
    if (positionChange > FLIP_THRESHOLD) {
      const currentDirection = currentThumbX > flipState.value.lastThumbX ? 1 : -1
      const now = Date.now()
      
      // 如果方向改变且距离上次翻转有足够时间间隔（避免抖动）
      if (currentDirection !== flipState.value.direction && now - flipState.value.lastFlipTime > 300) {
        flipState.value.flipCount++
        flipState.value.lastFlipTime = now
        flipState.value.direction = currentDirection
        
        console.log(`[PalmFlip] 检测到翻转 ${flipState.value.flipCount}/${TARGET_FLIPS}`)
      }
      
      flipState.value.lastThumbX = currentThumbX
    }

    // 判断是否完成
    if (flipState.value.flipCount >= TARGET_FLIPS) {
      return {
        isCompleted: true,
        progress: 100,
        feedback: `${side === 'left' ? '左手' : '右手'}翻转动作完成！`
      }
    }

    // 计算进度
    const flipProgress = (flipState.value.flipCount / TARGET_FLIPS) * 80
    const movementProgress = Math.min(20, totalChange * 200) // 基于总体移动幅度
    const progress = Math.round(flipProgress + movementProgress)

    let feedback = ''
    if (flipState.value.flipCount === 0) {
      feedback = `请开始${side === 'left' ? '左手' : '右手'}翻转，已检测到移动`
    } else {
      feedback = `${side === 'left' ? '左手' : '右手'}翻转进行中 ${flipState.value.flipCount}/${TARGET_FLIPS}`
    }

    return {
      isCompleted: false,
      progress: Math.max(0, Math.min(99, progress)),
      feedback
    }
  }

  /**
   * 重置翻转状态
   */
  const resetFlipState = () => {
    flipState.value = {
      initialThumbX: null,
      lastThumbX: null,
      flipCount: 0,
      lastFlipTime: 0,
      direction: 0
    }
  }

  /**
   * 获取所需关键点索引
   * @param {string} side - 动作侧别
   * @returns {Array} - 关键点索引数组
   */
  const getRequiredKeypoints = (side) => {
    const thumbIndex = side === 'left' ? KeyPointMapping.LEFT_HAND_THUMB_4 : KeyPointMapping.RIGHT_HAND_THUMB_4
    const wristIndex = side === 'left' ? KeyPointMapping.LEFT_WRIST : KeyPointMapping.RIGHT_WRIST
    return [thumbIndex, wristIndex]
  }

  return {
    detectPalmFlip,
    resetFlipState,
    getRequiredKeypoints,
    flipState,
    FLIP_THRESHOLD,
    TARGET_FLIPS
  }
}
