# 简化动作检测系统

## 概述

这是一个彻底简化的动作检测系统，专注于四个核心动作的最简检测逻辑。相比原来的复杂系统，新系统具有以下优势：

- **代码量减少80%**：从1500+行简化到不到500行
- **检测逻辑直观**：每个动作只有一个核心判断条件
- **性能提升**：移除复杂状态机，提高检测速度
- **易于维护**：清晰的模块化结构，便于调试和扩展

## 四个动作的简化检测逻辑

### 1. 摸肩膀 (shoulder_touch)
**核心逻辑**：仅判断手腕与对侧肩膀的距离
```javascript
distance(wrist, oppositeShoulder) < 0.15  // 归一化距离阈值
```
- **左手摸右肩**：计算左手腕到右肩膀的距离
- **右手摸左肩**：计算右手腕到左肩膀的距离
- **完成条件**：距离小于0.15（归一化坐标）

### 2. 举手臂 (arm_raise)
**核心逻辑**：判断肘关节角度和位置关系
```javascript
angle(wrist, elbow, shoulder) > 140° && elbow.y < shoulder.y
```
- **角度检测**：手腕-肘部-肩膀的夹角大于140°
- **位置检测**：肘部在肩膀上方（y坐标更小）
- **完成条件**：同时满足角度和位置要求

### 3. 手掌翻转 (palm_flip)
**核心逻辑**：根据训练侧别判断大拇指位置变化
```javascript
abs(thumb.x - initialThumb.x) > 0.1 && flipCount >= 3
```
- **位置变化**：大拇指x坐标变化超过0.1
- **翻转计数**：检测到3次方向变化
- **完成条件**：累计翻转次数达到目标

### 4. 触指 (finger_touch)
**核心逻辑**：判断大拇指是否依次与其他手指完成触摸
```javascript
distance(thumb, finger) < 0.05 for each finger in sequence
```
- **触摸顺序**：食指 → 中指 → 无名指 → 小指
- **触摸距离**：大拇指与目标手指距离小于0.05
- **保持时间**：每次触摸需保持500毫秒
- **完成条件**：完成4个手指的依次触摸

## 文件结构

```
simplified/
├── simplifiedActionEngine.js      # 核心引擎，提供基础几何计算
├── simplifiedShoulderTouch.js     # 摸肩膀检测器
├── simplifiedArmRaise.js          # 举手臂检测器
├── simplifiedPalmFlip.js          # 手掌翻转检测器
├── simplifiedFingerTouch.js       # 触指检测器
├── simplifiedActionDetector.js    # 统一检测管理器
├── testSimplifiedDetectors.js     # 测试文件
└── README.md                      # 本文档
```

## 统一返回格式

所有检测器都返回统一的结果格式：
```javascript
{
  isCompleted: boolean,    // 是否完成动作
  progress: number,        // 进度百分比 (0-100)
  feedback: string,        // 用户反馈文本
  actionType: string,      // 动作类型
  side: string            // 动作侧别
}
```

## 使用方法

### 基本使用
```javascript
import { useSimplifiedActionDetector } from './simplified/simplifiedActionDetector'

const detector = useSimplifiedActionDetector()

// 检测动作
const result = detector.detectAction('shoulder_touch', keypoints, 'left')
console.log(result.isCompleted, result.progress, result.feedback)
```

### 在训练会话中使用
```javascript
import { useSimplifiedTrainingSession } from '@/composables/useSimplifiedTrainingSession'

const session = useSimplifiedTrainingSession()
session.startTrainingSession()
```

## 性能特点

- **检测频率**：10FPS (100ms间隔)
- **响应延迟**：< 100ms
- **内存占用**：最小化状态存储
- **CPU使用**：优化的几何计算

## 调试和测试

### 运行测试
```javascript
// 在浏览器控制台中
testSimplifiedDetectors()  // 功能测试
performanceTest()          // 性能测试
```

### 获取检测器状态
```javascript
const state = detector.getDetectorState('palm_flip')
console.log(state) // { flipCount: 2, targetFlips: 3 }
```

## 与原系统的对比

| 特性 | 原系统 | 简化系统 |
|------|--------|----------|
| 代码行数 | 1500+ | < 500 |
| 检测器数量 | 4个复杂检测器 | 4个简单检测器 |
| 状态管理 | 多阶段状态机 | 最小化状态 |
| 检测逻辑 | 复杂几何计算 | 直接距离/角度判断 |
| 性能 | 较重 | 轻量级 |
| 维护性 | 复杂 | 简单直观 |

## 扩展指南

如需添加新的动作检测：

1. 创建新的检测器文件
2. 实现统一的检测接口
3. 在 `simplifiedActionDetector.js` 中注册
4. 添加相应的测试用例

每个检测器应该：
- 专注于单一动作的检测
- 使用归一化坐标进行计算
- 返回统一的结果格式
- 保持最小的状态存储
