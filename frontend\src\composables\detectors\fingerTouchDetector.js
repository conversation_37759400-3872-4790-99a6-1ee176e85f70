/**
 * 指尖对触动作检测器
 * 实现手部距离检测逻辑
 */
import { KeyPointMapping } from '@/utils/poseConstants'
import { useActionDetectionEngine } from '../useActionDetectionEngine'
import { ref } from 'vue'

export function useFingerTouchDetector() {
  const engine = useActionDetectionEngine()

  // 动作状态管理
  const actionState = ref({
    stage: 'waiting',        // waiting, approaching, touching, completed
    startTime: null,
    minDistance: null,
    progressScore: 0,
    isCompleted: false
  })

  // 动作所需关键点配置
  const REQUIRED_KEYPOINTS = {
    left: [KeyPointMapping.LEFT_HAND_WRIST, KeyPointMapping.LEFT_HAND_THUMB_4, KeyPointMapping.LEFT_HAND_INDEX_4],
    right: [KeyPointMapping.RIGHT_HAND_WRIST, KeyPointMapping.RIGHT_HAND_THUMB_4, KeyPointMapping.RIGHT_HAND_INDEX_4]
  }

  // 备用关键点（如果手部关键点不可用，使用手腕）
  const FALLBACK_KEYPOINTS = {
    left: [KeyPointMapping.LEFT_WRIST, KeyPointMapping.RIGHT_WRIST],
    right: [KeyPointMapping.LEFT_WRIST, KeyPointMapping.RIGHT_WRIST]
  }

  // 难度等级配置
  const DIFFICULTY_CONFIG = {
    easy: {
      touchThreshold: 0.08,      // 触摸距离阈值（相对肩宽）
      approachThreshold: 0.15,   // 接近距离阈值
      perfectThreshold: 0.05,    // 完美触摸阈值
      minScore: 50,              // 最低得分
      maxScore: 100              // 最高得分
    },
    medium: {
      touchThreshold: 0.06,
      approachThreshold: 0.12,
      perfectThreshold: 0.04,
      minScore: 60,
      maxScore: 100
    },
    hard: {
      touchThreshold: 0.05,
      approachThreshold: 0.1,
      perfectThreshold: 0.03,
      minScore: 70,
      maxScore: 100
    }
  }

  /**
   * 检测指尖对触动作（使用归一化坐标）
   * @param {Array} keypoints - 关键点数组（归一化坐标，格式：[x, y, confidence]）
   * @param {string} side - 动作侧面 ('left' | 'right')
   * @param {string} difficultyLevel - 难度等级
   * @returns {Object} - 检测结果
   */
  const detectFingerTouch = (keypoints, side = 'left', difficultyLevel = 'easy') => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]

    // 尝试使用精确的手部关键点
    let detectionResult = detectWithHandKeypoints(keypoints, side, config)

    // 如果手部关键点不可用，使用手腕作为备用
    if (detectionResult.stage === 'incomplete') {
      detectionResult = detectWithWristKeypoints(keypoints, side, config)
    }

    return detectionResult
  }

  /**
   * 使用手部关键点进行检测（归一化坐标）
   * @param {Array} keypoints - 关键点数组（归一化坐标）
   * @param {string} side - 动作侧面
   * @param {Object} config - 难度配置
   * @returns {Object} - 检测结果
   */
  const detectWithHandKeypoints = (keypoints, side, config) => {
    const requiredPoints = REQUIRED_KEYPOINTS[side]

    // 检查关键点完整性（使用归一化坐标）
    const completenessCheck = engine.checkRequiredKeypoints(keypoints, requiredPoints, side)
    if (!completenessCheck.isComplete) {
      return {
        accuracy: 0,
        stage: 'incomplete',
        feedback: completenessCheck.message,
        distance: Infinity,
        normalizedDistance: Infinity,
        detectionMethod: 'hand_keypoints',
        isCompleted: false
      }
    }

    // 获取手部关键点
    const leftThumb = keypoints[KeyPointMapping.LEFT_HAND_THUMB_4]
    const leftIndex = keypoints[KeyPointMapping.LEFT_HAND_INDEX_4]
    const rightThumb = keypoints[KeyPointMapping.RIGHT_HAND_THUMB_4]
    const rightIndex = keypoints[KeyPointMapping.RIGHT_HAND_INDEX_4]

    // 计算指尖距离（拇指和食指之间的最小距离）- 使用画布坐标
    const distances = [
      engine.calculateDistance(leftThumb, rightThumb, false),
      engine.calculateDistance(leftThumb, rightIndex, false),
      engine.calculateDistance(leftIndex, rightThumb, false),
      engine.calculateDistance(leftIndex, rightIndex, false)
    ].filter(d => d !== Infinity)

    if (distances.length === 0) {
      return {
        accuracy: 0,
        stage: 'invalid',
        feedback: '无法检测到手指位置',
        distance: Infinity,
        normalizedDistance: Infinity,
        detectionMethod: 'hand_keypoints'
      }
    }

    const minDistance = Math.min(...distances)
    const normalizedDistance = engine.normalizeDistance(minDistance, keypoints, false)

    if (normalizedDistance === Infinity) {
      return {
        accuracy: 0,
        stage: 'invalid',
        feedback: '无法计算指尖距离',
        distance: minDistance,
        normalizedDistance: Infinity,
        detectionMethod: 'hand_keypoints'
      }
    }

    // 计算得分和阶段
    const result = calculateFingerTouchScore(normalizedDistance, config)
    
    return {
      ...result,
      distance: minDistance,
      normalizedDistance,
      detectionMethod: 'hand_keypoints'
    }
  }

  /**
   * 使用手腕关键点进行备用检测（归一化坐标）
   * @param {Array} keypoints - 关键点数组（归一化坐标）
   * @param {string} side - 动作侧面
   * @param {Object} config - 难度配置
   * @returns {Object} - 检测结果
   */
  const detectWithWristKeypoints = (keypoints, side, config) => {
    const fallbackPoints = FALLBACK_KEYPOINTS[side]

    // 检查手腕关键点完整性（使用归一化坐标）
    const completenessCheck = engine.checkRequiredKeypoints(keypoints, fallbackPoints, side)
    if (!completenessCheck.isComplete) {
      return {
        accuracy: 0,
        stage: 'incomplete',
        feedback: '请保持双手在画面内',
        distance: Infinity,
        normalizedDistance: Infinity,
        detectionMethod: 'wrist_fallback',
        isCompleted: false
      }
    }

    // 获取手腕关键点
    const leftWrist = keypoints[KeyPointMapping.LEFT_WRIST]
    const rightWrist = keypoints[KeyPointMapping.RIGHT_WRIST]

    // 计算手腕距离（使用归一化坐标）
    const normalizedDistance = engine.calculateDistance(leftWrist, rightWrist)

    if (normalizedDistance === Infinity) {
      return {
        accuracy: 0,
        stage: 'invalid',
        feedback: '无法计算手部距离',
        distance: normalizedDistance,
        normalizedDistance: Infinity,
        detectionMethod: 'wrist_fallback',
        isCompleted: false
      }
    }

    // 使用调整后的阈值（手腕距离比指尖距离大）
    const adjustedConfig = {
      ...config,
      touchThreshold: config.touchThreshold * 2,
      approachThreshold: config.approachThreshold * 2,
      perfectThreshold: config.perfectThreshold * 2
    }

    // 计算得分和阶段
    const result = calculateFingerTouchScore(normalizedDistance, adjustedConfig, true)
    
    return {
      ...result,
      distance,
      normalizedDistance,
      detectionMethod: 'wrist_fallback'
    }
  }

  /**
   * 计算指尖对触得分和阶段
   * @param {number} normalizedDistance - 标准化距离
   * @param {Object} config - 难度配置
   * @param {boolean} isWristFallback - 是否为手腕备用检测
   * @returns {Object} - 得分和反馈
   */
  const calculateFingerTouchScore = (normalizedDistance, config, isWristFallback = false) => {
    const actionText = isWristFallback ? '双手' : '指尖'

    // 完美触摸
    if (normalizedDistance <= config.perfectThreshold) {
      return {
        accuracy: config.maxScore,
        stage: 'perfect_touch',
        feedback: `${actionText}完美对触！`
      }
    }

    // 良好触摸
    if (normalizedDistance <= config.touchThreshold) {
      const score = Math.round(
        config.maxScore - ((normalizedDistance - config.perfectThreshold) / 
        (config.touchThreshold - config.perfectThreshold)) * 20
      )
      return {
        accuracy: Math.max(config.minScore + 20, score),
        stage: 'good_touch',
        feedback: `${actionText}对触很好！`
      }
    }

    // 接近阶段
    if (normalizedDistance <= config.approachThreshold) {
      const score = Math.round(
        config.minScore + ((config.approachThreshold - normalizedDistance) / 
        (config.approachThreshold - config.touchThreshold)) * 20
      )
      return {
        accuracy: Math.max(config.minScore, score),
        stage: 'approaching',
        feedback: `${actionText}正在接近，继续！`
      }
    }

    // 准备阶段
    if (normalizedDistance <= 0.5) {
      const score = Math.round(
        (0.5 - normalizedDistance) / (0.5 - config.approachThreshold) * config.minScore
      )
      return {
        accuracy: Math.max(0, score),
        stage: 'preparing',
        feedback: `请将${actionText}靠近`
      }
    }

    // 距离太远
    return {
      accuracy: 0,
      stage: 'too_far',
      feedback: `请将${actionText}相互靠近`
    }
  }

  /**
   * 检查动作是否完成
   * @param {Object} detectionResult - 检测结果
   * @param {string} difficultyLevel - 难度等级
   * @returns {boolean} - 是否完成
   */
  const isActionCompleted = (detectionResult, difficultyLevel = 'easy') => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]
    return detectionResult.accuracy >= config.minScore + 20 && 
           ['good_touch', 'perfect_touch'].includes(detectionResult.stage)
  }

  /**
   * 获取动作指导建议
   * @param {Object} detectionResult - 检测结果
   * @param {string} side - 动作侧面
   * @returns {string} - 指导建议
   */
  const getGuidance = (detectionResult, side) => {
    const isWristFallback = detectionResult.detectionMethod === 'wrist_fallback'
    const actionText = isWristFallback ? '双手' : '指尖'

    switch (detectionResult.stage) {
      case 'incomplete':
        return detectionResult.feedback
      case 'too_far':
        return `将${actionText}慢慢靠近`
      case 'preparing':
        return `继续将${actionText}相互靠近`
      case 'approaching':
        return `很好！${actionText}快要接触了`
      case 'good_touch':
        return `保持${actionText}接触的姿势`
      case 'perfect_touch':
        return `完美！保持这个姿势`
      default:
        return '请按照示范动作进行'
    }
  }

  /**
   * 重置检测器状态
   */
  const resetDetector = () => {
    actionState.value = {
      stage: 'waiting',
      startTime: null,
      minDistance: null,
      progressScore: 0,
      isCompleted: false
    }
  }

  return {
    // 主要检测方法
    detectFingerTouch,
    isActionCompleted,
    getGuidance,

    // 状态管理
    actionState,
    resetDetector,

    // 配置
    REQUIRED_KEYPOINTS,
    FALLBACK_KEYPOINTS,
    DIFFICULTY_CONFIG
  }
}
