<template>
  <div class="h-screen bg-gray-900 text-white flex p-4 gap-4 overflow-hidden">
    <!-- 背景辉光 (保持不变) -->
    <div class="absolute top-[-50%] left-[-10%] w-3/4 h-3/4 bg-blue-500/10 rounded-full blur-3xl animate-pulse-slow -z-10"></div>
    <div class="absolute bottom-[-50%] right-[-10%] w-3/4 h-3/4 bg-purple-600/10 rounded-full blur-3xl animate-pulse-slow-delay -z-10"></div>

    <!-- 左侧信息栏 (保持美化后的样式) -->
    <aside class="w-80 bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl flex flex-col p-4 space-y-4 animate-fade-in-left">
      <!-- 用户与系统状态 -->
      <div class="bg-black/20 rounded-lg p-4 flex-shrink-0">
        <div v-if="userInfo" class="flex items-center space-x-4 mb-4">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <User class="text-2xl" />
          </div>
          <div>
            <p class="font-semibold text-lg">{{ userInfo.patient_name }}</p>
            <p class="text-xs text-gray-400">ID: {{ userInfo.patient_id }}</p>
          </div>
        </div>
        <div :class="['flex items-center p-2 rounded-md text-sm font-medium', getStateStatusClass(currentState)]">
          <div :class="['w-2.5 h-2.5 rounded-full mr-2', getStateIndicatorClass(currentState)]"></div>
          <span>{{ getStateDescription(currentState) }}</span>
        </div>
      </div>
      
      <!-- 训练任务列表 -->
      <div class="flex-1 bg-black/20 rounded-lg p-4 flex flex-col min-h-0">
        <h3 class="font-semibold mb-4 text-lg flex-shrink-0">训练任务</h3>
        <div class="flex-1 overflow-y-auto pr-2 custom-scrollbar">
          <transition-group name="action-list" tag="div" class="space-y-3">
            <div
              v-for="(action, index) in trainingStore.actionList"
              :key="action.action_id || index"
              :class="['p-3 rounded-lg transition-all duration-500 border', getActionItemClass(index)]"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div :class="['w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold', getActionStatusIconClass(index)]">
                    <Check v-if="index < currentActionIndex" />
                    <DArrowRight v-else-if="index === currentActionIndex" />
                    <span v-else>{{ index + 1 }}</span>
                  </div>
                  <span class="font-medium">{{ getActionDisplayName(action) }}</span>
                </div>
                <span class="text-xs font-mono">{{ getSideDisplayName(action.side) }}</span>
              </div>
            </div>
          </transition-group>
        </div>
      </div>
    </aside>

    <!-- 右侧主训练区 【核心布局修正】 -->
    <main class="flex-1 grid grid-cols-2 grid-rows-2 gap-4 animate-fade-in-right">
      <!-- 左上：标准动作演示 -->
      <div class="col-span-1 row-span-1 bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl flex flex-col overflow-hidden">
        <div class="p-3 border-b border-white/10 flex-shrink-0">
          <h3 class="font-semibold text-center">标准动作演示</h3>
        </div>
        <div class="flex-1 relative bg-black/30">
          <video v-if="videoUrl" :src="videoUrl" ref="standardVideoRef" class="w-full h-full object-contain" loop autoplay muted></video>
          <div v-else class="flex items-center justify-center h-full text-gray-500"><p>暂无演示视频</p></div>
        </div>
      </div>
<!-- 右侧跨两行：实时画面 -->
      <div class="col-span-1 row-span-2 bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl flex flex-col overflow-hidden">
        <div class="p-3 border-b border-white/10 flex-shrink-0">
          <h3 class="font-semibold text-center">实时画面</h3>
        </div>
        <div class="flex-1 relative bg-black/30">
          <VideoStream ref="videoStreamComponentRef" />
          <PoseOverlay :target-ref="videoStreamComponentRef" />
          <div v-if="workflowStore.isPaused" class="absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-20 text-2xl font-bold">
            训练已暂停
          </div>
        </div>
      </div>
      <!-- 左下：增强实时反馈 -->
      <div class="col-span-1 row-span-1 bg-black/20 backdrop-blur-xl border border-white/10 rounded-2xl flex flex-col overflow-hidden p-4">
        <h3 class="font-semibold text-center mb-3 text-lg">实时反馈</h3>

        <!-- 综合得分仪表盘 -->
        <div class="flex items-center justify-center mb-10">
          <div class="relative w-28 h-28">
            <svg class="w-full h-full" viewBox="0 0 100 100">
              <circle class="text-gray-700" stroke-width="6" stroke="currentColor" fill="transparent" r="40" cx="50" cy="50" />
              <circle
                class="transition-all duration-500 ease-out"
                :class="overallScoreColor"
                stroke-width="6"
                :stroke-dasharray="overallCircumference"
                :stroke-dashoffset="overallScoreOffset"
                stroke-linecap="round" stroke="currentColor" fill="transparent"
                r="40" cx="50" cy="50" transform="rotate(-90 50 50)"
              />
            </svg>
            <div class="absolute inset-0 flex flex-col items-center justify-center">
              <span class="text-2xl font-bold text-white">{{ enhancedScores.overall }}</span>
              <span class="text-xs text-gray-400">综合</span>
            </div>
          </div>
        </div>

        <!-- 多维度评分 -->
        <div class="grid grid-cols-4 gap-2 mb-3">
          <!-- 准确性 -->
          <div class="text-center ">
            <div class="relative w-16 h-16 mx-auto mb-1">
              <svg class="w-full h-full" viewBox="0 0 100 100">
                <circle class="text-gray-600" stroke-width="8" stroke="currentColor" fill="transparent" r="35" cx="50" cy="50" />
                <circle
                  class="transition-all duration-300 text-blue-400"
                  stroke-width="8"
                  :stroke-dasharray="smallCircumference"
                  :stroke-dashoffset="getScoreOffset(enhancedScores.accuracy)"
                  stroke-linecap="round" stroke="currentColor" fill="transparent"
                  r="35" cx="50" cy="50" transform="rotate(-90 50 50)"
                />
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <span class="text-sm font-bold text-white">{{ enhancedScores.accuracy }}</span>
              </div>
            </div>
            <p class="text-xs text-gray-300">准确性</p>
          </div>

          <!-- 稳定性 -->
          <div class="text-center">
            <div class="relative w-16 h-16 mx-auto mb-1">
              <svg class="w-full h-full" viewBox="0 0 100 100">
                <circle class="text-gray-600" stroke-width="8" stroke="currentColor" fill="transparent" r="35" cx="50" cy="50" />
                <circle
                  class="transition-all duration-300 text-green-400"
                  stroke-width="8"
                  :stroke-dasharray="smallCircumference"
                  :stroke-dashoffset="getScoreOffset(enhancedScores.stability)"
                  stroke-linecap="round" stroke="currentColor" fill="transparent"
                  r="35" cx="50" cy="50" transform="rotate(-90 50 50)"
                />
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <span class="text-sm font-bold text-white">{{ enhancedScores.stability }}</span>
              </div>
            </div>
            <p class="text-xs text-gray-300">稳定性</p>
          </div>

          <!-- 完整性 -->
          <div class="text-center">
            <div class="relative w-16 h-16 mx-auto mb-1">
              <svg class="w-full h-full" viewBox="0 0 100 100">
                <circle class="text-gray-600" stroke-width="8" stroke="currentColor" fill="transparent" r="35" cx="50" cy="50" />
                <circle
                  class="transition-all duration-300 text-yellow-400"
                  stroke-width="8"
                  :stroke-dasharray="smallCircumference"
                  :stroke-dashoffset="getScoreOffset(enhancedScores.completeness)"
                  stroke-linecap="round" stroke="currentColor" fill="transparent"
                  r="35" cx="50" cy="50" transform="rotate(-90 50 50)"
                />
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <span class="text-sm font-bold text-white">{{ enhancedScores.completeness }}</span>
              </div>
            </div>
            <p class="text-xs text-gray-300">完整性</p>
          </div>

          <!-- 渐进式得分 -->
          <div class="text-center">
            <div class="relative w-16 h-16 mx-auto mb-1">
              <svg class="w-full h-full" viewBox="0 0 100 100">
                <circle class="text-gray-600" stroke-width="8" stroke="currentColor" fill="transparent" r="35" cx="50" cy="50" />
                <circle
                  class="transition-all duration-300 text-purple-400"
                  stroke-width="8"
                  :stroke-dasharray="smallCircumference"
                  :stroke-dashoffset="getScoreOffset(enhancedScores.progressScore)"
                  stroke-linecap="round" stroke="currentColor" fill="transparent"
                  r="35" cx="50" cy="50" transform="rotate(-90 50 50)"
                />
              </svg>
              <div class="absolute inset-0 flex items-center justify-center">
                <span class="text-sm font-bold text-white">{{ enhancedScores.progressScore }}</span>
              </div>
            </div>
            <p class="text-xs text-gray-300">进度</p>
          </div>
        </div>

        <!-- 智能反馈信息 -->
        <div class="flex-1 flex flex-col justify-center ">
          <!-- 动作阶段指示器 -->
          <div v-if="currentActionStage" class="text-center mb-2">
            <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-all duration-300"
                 :class="getActionStageStyle(currentActionStage)">
              <span class="mr-2">{{ getActionStageIcon(currentActionStage) }}</span>
              {{ getActionStageText(currentActionStage) }}
            </div>
          </div>

          <!-- 动作评价 -->
          <div class="text-center mb-2">
            <p class="text-xl font-medium transition-all duration-300 text-blue-300">
              {{ currentFeedback.text }}
            </p>
          </div>
          <!-- 智能指导 -->
          <transition name="fade">
            <div v-if="shouldShowGuidance" class="bg-blue-500/20 border border-blue-400/30 rounded-lg p-2 mb-2">
              <p class="text-xl text-blue-200 text-center leading-relaxed">
                {{ currentGuidance }}
              </p>
            </div>
          </transition>
          <!-- 画面完整性提示 -->
          <transition name="fade">
            <div v-if="!visibilityStatus.isComplete" class="bg-red-500/20 border border-red-400/30 rounded-lg p-2">
              <p class=" text-2xl text-red-200 text-center leading-relaxed">
                {{ visibilityStatus.message }}
              </p>
            </div>
          </transition>

          <!-- 准备就绪动画提示 -->
          <transition name="ready-pulse">
            <div v-if="simplifiedTrainingSession.isTrainingActive.value && simplifiedTrainingSession.currentProgress.value === 0"
                 class="bg-green-500/30 border-2 border-green-400 rounded-lg p-3 mb-2 animate-pulse">
              <div class="flex items-center justify-center space-x-2">
                <div class="w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
                <p class="text-sm text-green-200 font-medium text-center">
                  ✓ 准备就绪！可以开始动作了
                </p>
                <div class="w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
              </div>
            </div>
          </transition>
        </div>
      </div>
    </main>
    <!-- 动作介绍模态框  -->
    <Transition name="modal">
      <div v-if="showActionIntroDialog" class="fixed inset-0 bg-black/70 backdrop-blur-md z-50 flex items-center justify-center p-4 sm:p-8">
        <div class="w-full max-w-6xl bg-gray-900/80 border border-white/10 rounded-2xl shadow-2xl flex flex-col overflow-hidden animate-zoom-in">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 p-10">
            <!-- 左侧视频 -->
            <div class="flex flex-col">
              <!-- 【改动点 3】: 增大标题字号 -->
              <h2 class="text-3xl font-bold mb-4 text-center">动作演示</h2>
              <div class="flex-1 bg-black rounded-lg overflow-hidden aspect-video shadow-lg">
                <video 
                  v-if="introVideoUrl" 
                  :src="introVideoUrl"
                  ref="introVideoRef"
                  class="w-full h-full object-contain"
                  autoplay
                  @ended="handleIntroVideoEnded"
                ></video>
                <div v-else class="h-full flex items-center justify-center text-gray-500">暂无视频</div>
              </div>
            </div>
            <!-- 右侧信息 -->
            <div class="flex flex-col justify-center">
              <h2 class="text-4xl font-bold mb-6">{{ getActionDisplayName(trainingStore.currentAction) }}</h2>
              <div class="space-y-6 text-lg text-gray-300">
                <div class="flex items-center">
                  <strong class="font-semibold text-white w-24 flex-shrink-0">侧别:</strong>
                  <span>{{ getSideDisplayName(trainingStore.currentAction?.side) }}</span>
                </div>
                <div class="flex items-center">
                  <strong class="font-semibold text-white w-24 flex-shrink-0">难度:</strong>
                  <span :class="[
                    'px-3 py-1 text-sm rounded-full',
                    getDifficultyInfo(trainingStore.currentAction?.difficulty_level).colorClass.replace('/20', '/30') // 背景色更深一点
                  ]">
                    {{ getDifficultyInfo(trainingStore.currentAction?.difficulty_level).text }}
                  </span>
                </div>
                <div class="max-h-48 overflow-y-auto custom-scrollbar pr-2">
                  <strong class="font-semibold text-white block mb-2">动作要点:</strong>
                  <p class="leading-relaxed">{{ getActionDescription(trainingStore.currentAction) }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="p-5 bg-black/20 border-t border-white/10 mt-auto">
            <div class="flex items-center justify-between">
              <p class="text-base text-gray-400">视频播放完成后将自动开始</p>
              <button @click="handleManualCloseIntro" class="px-8 py-3 bg-blue-600 hover:bg-blue-500 rounded-lg font-semibold transition-colors text-base">
                我已了解，开始训练
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useTrainingStore } from '@/stores/training';
import { useWorkflowStore } from '@/stores/workflow';
import { usePatientStore } from '@/stores/patient';
import { useStateTransition } from '@/composables/useStateTransition';
import { useSimplifiedTrainingSession } from '@/composables/useSimplifiedTrainingSession';
import { User, Check, DArrowRight } from '@element-plus/icons-vue';
import VideoStream from '@/components/VideoStream.vue';
import PoseOverlay from '@/components/PoseOverlay.vue';

const trainingStore = useTrainingStore();
const workflowStore = useWorkflowStore();
const patientStore = usePatientStore();
const stateTransition = useStateTransition();
const simplifiedTrainingSession = useSimplifiedTrainingSession();

// --- UI状态 ---
const showActionIntroDialog = ref(false);
const introVideoUrl = ref(''); // 弹窗中的视频URL
const videoUrl = ref(''); // 训练页标准视频URL

// --- Template Refs ---
const videoStreamComponentRef = ref(null);
const standardVideoRef = ref(null);
const introVideoRef = ref(null);

// --- 计算属性 ---
const currentActionIndex = computed(() => {
  if (!trainingStore.currentAction || !trainingStore.actionList.length) return -1;
  // 确保使用唯一的标识符进行查找
  const idKey = trainingStore.actionList[0]?.action_id ? 'action_id' : 'action_type';
  return trainingStore.actionList.findIndex(a => a[idKey] === trainingStore.currentAction[idKey]);
});

// 简化版评分计算属性
const enhancedScores = computed(() => {
  const progress = simplifiedTrainingSession.currentProgress.value;
  // 简化的分数结构，基于进度值
  return {
    overall: progress,
    accuracy: progress,
    stability: progress,
    completeness: progress,
    progressScore: progress
  };
});
const currentFeedback = computed(() => ({
  text: simplifiedTrainingSession.currentFeedback.value,
  type: 'info'
}));
const currentGuidance = computed(() => currentFeedback.value?.text || '');

// 基于当前反馈判断画面完整性
const visibilityStatus = computed(() => {
  const feedback = currentFeedback.value;
  if (feedback && feedback.text) {
    // 检查是否包含画面完整性相关的提示
    const isIncompleteMessage = feedback.text.includes('画面') ||
                               feedback.text.includes('保持') ||
                               feedback.text.includes('完整展示') ||
                               feedback.text.includes('请确保');

    return {
      isComplete: !isIncompleteMessage,
      message: isIncompleteMessage ? feedback.text : ''
    };
  }
  return { isComplete: true, message: '' };
});

const shouldShowGuidance = computed(() => {
  return currentFeedback.value &&
         currentFeedback.value.text &&
         currentFeedback.value.text !== '准备开始训练' &&
         simplifiedTrainingSession.currentFeedback.value !== ''
});

// 动作阶段状态
const currentActionStage = computed(() => {
  // 简化版本，基于进度判断阶段
  const progress = simplifiedTrainingSession.currentProgress.value;
  if (progress >= 95) return 'completed';
  if (progress >= 70) return 'peak';
  if (progress >= 30) return 'approaching';
  return 'waiting';
});

// 圆形进度条计算
const overallCircumference = 2 * Math.PI * 40;
const smallCircumference = 2 * Math.PI * 35;

const overallScoreOffset = computed(() => {
  return overallCircumference - (enhancedScores.value.overall / 100) * overallCircumference;
});

const getScoreOffset = (score) => {
  return smallCircumference - (score / 100) * smallCircumference;
};

// 综合得分颜色
const overallScoreColor = computed(() => {
  const score = enhancedScores.value.overall;
  if (score >= 95) return 'text-green-400';
  if (score >= 80) return 'text-blue-400';
  if (score >= 60) return 'text-yellow-400';
  return 'text-red-400';
});

// 增强版反馈
const enhancedFeedback = computed(() => {
  const score = enhancedScores.value.overall;
  if (score >= 95) return { text: '完美！', colorClass: 'text-green-400' };
  if (score >= 80) return { text: '优秀', colorClass: 'text-blue-400' };
  if (score >= 60) return { text: '良好', colorClass: 'text-yellow-400' };
  return { text: '加油！每日坚持训练，康复效果更好', colorClass: 'text-red-400' };
});

// 动作阶段处理方法
const getActionStageText = (stage) => {
  const stageTexts = {
    'resting': '准备中',
    'approaching': '靠近中',
    'peak': '到达峰值',
    'returning': '归位中',
    'completed': '已完成'
  };
  return stageTexts[stage] || '检测中';
};

const getActionStageIcon = (stage) => {
  const stageIcons = {
    'resting': '⏳',
    'approaching': '🎯',
    'peak': '⭐',
    'returning': '🔄',
    'completed': '✅'
  };
  return stageIcons[stage] || '🔍';
};

const getActionStageStyle = (stage) => {
  const stageStyles = {
    'resting': 'bg-gray-500/20 text-gray-300 border border-gray-400/30',
    'approaching': 'bg-blue-500/20 text-blue-300 border border-blue-400/30',
    'peak': 'bg-yellow-500/20 text-yellow-300 border border-yellow-400/30',
    'returning': 'bg-purple-500/20 text-purple-300 border border-purple-400/30',
    'completed': 'bg-green-500/20 text-green-300 border border-green-400/30'
  };
  return stageStyles[stage] || 'bg-gray-500/20 text-gray-300 border border-gray-400/30';
};

// 从各个store获取数据的计算属性
const userInfo = computed(() => patientStore.userInfo);
const currentState = computed(() => workflowStore.currentState);

// --- 方法 ---

// 状态和样式的辅助函数
const getStateStatusClass = (state) => ({ 'training': 'bg-green-500/20 text-green-300', 'preparation': 'bg-yellow-500/20 text-yellow-300' }[state] || 'bg-gray-500/20 text-gray-300');
const getStateIndicatorClass = (state) => ({ 'training': 'bg-green-400 animate-pulse', 'preparation': 'bg-yellow-400' }[state] || 'bg-gray-400');
const getStateDescription = (state) => ({ 'training': '训练进行中', 'preparation': '准备新动作', 'pause': '训练已暂停', 'introduction': '任务介绍中' }[state] || '等待指令');

const getDifficultyInfo = (level) => {
  const defaultInfo = { text: '常规', colorClass: 'bg-gray-500/20 text-gray-300' };
  if (!level) return defaultInfo;
  
  switch (level.toLowerCase()) {
    case 'easy':
      return { text: '简单', colorClass: 'bg-green-500/20 text-green-300' };
    case 'medium':
      return { text: '中等', colorClass: 'bg-yellow-500/20 text-yellow-300' };
    case 'hard':
      return { text: '困难', colorClass: 'bg-red-500/20 text-red-300' };
    default:
      return defaultInfo;
  }
};
const getActionDisplayName = (action) => action?.action_type?.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) || '未知动作';
const getSideDisplayName = (side) => ({ 'left': '左侧', 'right': '右侧' }[side] || '双侧');
const getActionDescription = (action) => action?.description || '暂无详细描述，请参考标准视频。';

const getActionItemClass = (index) => {
  if (index === currentActionIndex.value) return 'border-blue-500 bg-blue-500/20 scale-105 shadow-lg';
  if (index < currentActionIndex.value) return 'border-transparent bg-transparent opacity-50';
  return 'border-transparent bg-transparent opacity-80';
};
const getActionStatusIconClass = (index) => {
  if (index === currentActionIndex.value) return 'bg-blue-500 text-white';
  if (index < currentActionIndex.value) return 'bg-green-500/80 text-white';
  return 'bg-gray-600 text-gray-300';
};

// --- 事件处理器 ---
const closeIntroAndStart = () => {
  if (!showActionIntroDialog.value) return; // 防止重复调用
  showActionIntroDialog.value = false;
  console.log('介绍结束，转换到训练状态');
  stateTransition.handleActionIntroComplete();
};

const handleManualCloseIntro = () => {
  closeIntroAndStart();
};
const handleIntroVideoEnded = () => {
  setTimeout(() => closeIntroAndStart(), 500);
};


// 【核心逻辑修正】使用一个专门的函数来触发弹窗
const triggerIntroDialog = (action) => {
  if (action && action.video_url) {
    introVideoUrl.value = action.video_url;
  } else {
    // 如果没有视频，也显示弹窗，但给个默认提示
    introVideoUrl.value = '';
    console.warn(`动作 ${action?.action_type} 没有提供 video_url`);
  }
  
  // 更新主训练区的标准视频
  videoUrl.value = action?.video_url || '';

  // 显示弹窗
  showActionIntroDialog.value = true;

  // 播放视频
  nextTick(() => {
    introVideoRef.value?.play().catch(e => console.error("介绍视频自动播放失败:", e));
  });
};
watch(
  () => [workflowStore.currentState, trainingStore.currentAction],
  // 【核心修正】为解构的参数提供默认的空数组 []
  ([newState, newAction], [oldState, oldAction] = []) => {
    // 关键条件：只有当状态是 'preparation' 时，我们才考虑弹窗
    if (newState === 'preparation') {
      // 两种情况需要弹窗：
      // 1. 刚刚进入 preparation 状态 (例如从 introduction 切换过来)
      // 2. 已经在 preparation 状态，但动作更新了 (action_id 或其他唯一标识)
      // 使用可选链 (?.) 增加代码健壮性
      if (newAction && (newState !== oldState || newAction?.action_id !== oldAction?.action_id)) {
        console.log(`检测到准备新动作: ${newAction.action_type}，准备弹出介绍框。`);
        triggerIntroDialog(newAction);
      }
    } else {
      // 如果状态不是 'preparation'，确保弹窗是关闭的
      if (showActionIntroDialog.value) {
        showActionIntroDialog.value = false;
      }
      // 并且，如果不在准备阶段，也应该更新主视频区
      if (newAction) {
          videoUrl.value = newAction.video_url || '';
      }
    }
  },
  { deep: true, immediate: true } // immediate: true 确保组件加载时立即执行一次检查
);

// 组件挂载时启动增强训练会话
onMounted(() => {
  console.log('[TrainingView] 组件挂载，启动简化训练会话')

  // 如果当前状态是training，立即启动训练会话
  if (workflowStore.currentState === 'training') {
    simplifiedTrainingSession.startTrainingSession()
  }
})

// 监听训练状态变化
watch(
  () => workflowStore.currentState,
  (newState, oldState) => {
    console.log(`[TrainingView] 状态变化: ${oldState} -> ${newState}`)
    console.log(`[TrainingView] 当前动作:`, trainingStore.currentAction)

    if (newState === 'training') {
      // 检查当前动作是否存在
      if (!trainingStore.currentAction) {
        console.warn('[TrainingView] 警告：进入训练状态但没有当前动作，尝试初始化')
        trainingStore.initializeActions()
      }

      // 进入训练状态，启动增强训练会话
      console.log('[TrainingView] 启动增强训练会话，当前动作:', trainingStore.currentAction?.action_type)
      enhancedTrainingSession.startTrainingSession()
    } else if (oldState === 'training') {
      // 离开训练状态，停止训练会话
      enhancedTrainingSession.stopTrainingSession()
    }
  }
)

// 确保组件卸载时清理资源
onUnmounted(() => {
  console.log('[TrainingView] 组件卸载，清理增强训练会话')
  enhancedTrainingSession.stopTrainingSession()
});
</script>

<style scoped>
/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.4);
}

/* 介绍弹窗动画 */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.4s ease;
}
.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}
.modal-enter-active .animate-zoom-in,
.modal-leave-active .animate-zoom-in {
  transition: all 0.4s ease;
}
.modal-enter-from .animate-zoom-in {
  transform: scale(0.9);
  opacity: 0;
}
.modal-leave-to .animate-zoom-in {
  transform: scale(0.9);
  opacity: 0;
}

/* 任务列表切换动画 */
.action-list-move,
.action-list-enter-active,
.action-list-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}
.action-list-enter-from,
.action-list-leave-to {
  opacity: 0;
  transform: scale(0.9) translateX(30px);
}
.action-list-leave-active {
  position: absolute;
}

/* 准备就绪动画 */
.ready-pulse-enter-active,
.ready-pulse-leave-active {
  transition: all 0.5s ease-in-out;
}

.ready-pulse-enter-from {
  opacity: 0;
  transform: scale(0.8);
}

.ready-pulse-leave-to {
  opacity: 0;
  transform: scale(1.1);
}

/* 背景辉光动画 */
@keyframes pulse-slow {
  50% { opacity: 0.15; }
}
@keyframes pulse-slow-delay {
  50% { opacity: 0.15; }
}
.animate-pulse-slow { animation: pulse-slow 8s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-pulse-slow-delay { animation: pulse-slow 8s cubic-bezier(0.4, 0, 0.6, 1) infinite 4s; }

/* 页面进入动画 */
@keyframes fade-in-left { from { opacity: 0; transform: translateX(-20px); } to { opacity: 1; transform: translateX(0); } }
@keyframes fade-in-right { from { opacity: 0; transform: translateX(20px); } to { opacity: 1; transform: translateX(0); } }
.animate-fade-in-left { animation: fade-in-left 0.7s 0.1s ease-out forwards; }
.animate-fade-in-right { animation: fade-in-right 0.7s 0.3s ease-out forwards; }
</style>