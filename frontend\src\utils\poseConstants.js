/**
 * COCO-WholeBody (133个关键点) 的骨架连接定义
 *
 * 这个数组定义了哪些关键点应该用线连接起来，形成一个可视化的骨架。
 * 每个子数组 [id1, id2] 代表在关键点id1和id2之间画一条线。
 *
 * 数据源: 基于您提供的关键点ID和名称进行逻辑连接。
 * - 身体和四肢 (Body & Limbs)
 * - 脚部 (Feet)
 * - 手部 (Hands)
 * - 面部 (Face) 部分被省略，不进行绘制。
 */
export const COCO_WHOLEBODY_CONNECTIONS = [
  // 1. 躯干 (Torso)
  [5, 6], // left_shoulder -> right_shoulder
  [5, 11], // left_shoulder -> left_hip
  [6, 12], // right_shoulder -> right_hip
  [11, 12], // left_hip -> right_hip

  // 2. 头部 (Head) - 简化连接
  [1, 2], // left_eye -> right_eye
  [1, 3], // left_eye -> left_ear
  [2, 4], // right_eye -> right_ear
  // 连接头部到身体 (使用鼻子作为连接点)
  [0, 1], // nose -> left_eye
  [0, 2], // nose -> right_eye
  // [0, 5], // nose -> left_shoulder (可选，但有助于形成颈部感)
  // [0, 6], // nose -> right_shoulder (可选，但有助于形成颈部感)

  // 3. 左臂 (Left Arm)
  [5, 7], // left_shoulder -> left_elbow
  [7, 9], // left_elbow -> left_wrist

  // 4. 右臂 (Right Arm)
  [6, 8], // right_shoulder -> right_elbow
  [8, 10], // right_elbow -> right_wrist

  // 5. 左腿 (Left Leg)
  [11, 13], // left_hip -> left_knee
  [13, 15], // left_knee -> left_ankle

  // 6. 右腿 (Right Leg)
  [12, 14], // right_hip -> right_knee
  [14, 16], // right_knee -> right_ankle

  // 7. 左脚 (Left Foot) - 形成一个三角形
  [15, 17], // left_ankle -> left_big_toe
  [15, 19], // left_ankle -> left_heel
  [17, 18], // left_big_toe -> left_small_toe

  // 8. 右脚 (Right Foot) - 形成一个三角形
  [16, 20], // right_ankle -> right_big_toe
  [16, 22], // right_ankle -> right_heel
  [20, 21], // right_big_toe -> right_small_toe

  // 9. 左手 (Left Hand)
  // [9, 91], // left_wrist -> left_hand_root

  // 左手手掌 (连接指根形成手掌)
  [91, 96], // left_hand_root -> left_forefinger1
  [96, 100], // left_forefinger1 -> left_middle_finger1
  [100, 104], // left_middle_finger1 -> left_ring_finger1
  [104, 108], // left_ring_finger1 -> left_pinky_finger1
  [108, 91], // left_pinky_finger1 -> left_hand_root

  // 左手拇指
  [91, 92], // left_hand_root -> left_thumb1
  [92, 93], // left_thumb1 -> left_thumb2
  [93, 94], // left_thumb2 -> left_thumb3
  [94, 95], // left_thumb3 -> left_thumb4

  // 左手食指
  [96, 97], // left_forefinger1 -> left_forefinger2
  [97, 98], // left_forefinger2 -> left_forefinger3
  [98, 99], // left_forefinger3 -> left_forefinger4

  // 左手中指
  [100, 101], // left_middle_finger1 -> left_middle_finger2
  [101, 102], // left_middle_finger2 -> left_middle_finger3
  [102, 103], // left_middle_finger3 -> left_middle_finger4

  // 左手无名指
  [104, 105], // left_ring_finger1 -> left_ring_finger2
  [105, 106], // left_ring_finger2 -> left_ring_finger3
  [106, 107], // left_ring_finger3 -> left_ring_finger4

  // 左手小指
  [108, 109], // left_pinky_finger1 -> left_pinky_finger2
  [109, 110], // left_pinky_finger2 -> left_pinky_finger3
  [110, 111], // left_pinky_finger3 -> left_pinky_finger4

  // 10. 右手 (Right Hand)
  // [10, 112], // right_wrist -> right_hand_root

  // 右手手掌 (连接指根形成手掌)
  [112, 117], // right_hand_root -> right_forefinger1
  [117, 121], // right_forefinger1 -> right_middle_finger1
  [121, 125], // right_middle_finger1 -> right_ring_finger1
  [125, 129], // right_ring_finger1 -> right_pinky_finger1
  [129, 112], // right_pinky_finger1 -> right_hand_root

  // 右手拇指
  [112, 113], // right_hand_root -> right_thumb1
  [113, 114], // right_thumb1 -> right_thumb2
  [114, 115], // right_thumb2 -> right_thumb3
  [115, 116], // right_thumb3 -> right_thumb4

  // 右手食指
  [117, 118], // right_forefinger1 -> right_forefinger2
  [118, 119], // right_forefinger2 -> right_forefinger3
  [119, 120], // right_forefinger3 -> right_forefinger4

  // 右手中指
  [121, 122], // right_middle_finger1 -> right_middle_finger2
  [122, 123], // right_middle_finger2 -> right_middle_finger3
  [123, 124], // right_middle_finger3 -> right_middle_finger4

  // 右手无名指
  [125, 126], // right_ring_finger1 -> right_ring_finger2
  [126, 127], // right_ring_finger2 -> right_ring_finger3
  [127, 128], // right_ring_finger3 -> right_ring_finger4

  // 右手小指
  [129, 130], // right_pinky_finger1 -> right_pinky_finger2
  [130, 131], // right_pinky_finger2 -> right_pinky_finger3
  [131, 132], // right_pinky_finger3 -> right_pinky_finger4
];
export const KeyPointMapping = {
    NOSE : 0,
    LEFT_EYE : 1,
    RIGHT_EYE : 2,
    LEFT_EAR : 3,
    RIGHT_EAR : 4,
    LEFT_SHOULDER : 5,
    RIGHT_SHOULDER : 6,
    LEFT_ELBOW : 7,
    RIGHT_ELBOW : 8,
    LEFT_WRIST : 9,
    RIGHT_WRIST : 10,
    LEFT_HIP : 11,
    RIGHT_HIP : 12,
    LEFT_KNEE : 13,
    RIGHT_KNEE : 14,
    LEFT_ANKLE : 15,
    RIGHT_ANKLE : 16,
 
    LEFT_BIG_TOE : 17,
    LEFT_SMALL_TOE : 18,
    LEFT_HEEL : 19,
    RIGHT_BIG_TOE : 20,
    RIGHT_SMALL_TOE : 21,
    RIGHT_HEEL : 22,
    FACE_START : 23,
    FACE_END : 90,
    LEFT_HAND_START : 91,
    LEFT_HAND_END : 111,
    LEFT_HAND_WRIST : 91,
    LEFT_HAND_THUMB_1 : 92,
    LEFT_HAND_THUMB_2 : 93,
    LEFT_HAND_THUMB_3 : 94,
    LEFT_HAND_THUMB_4 : 95,
    LEFT_HAND_INDEX_1 : 96,
    LEFT_HAND_INDEX_2 : 97,
    LEFT_HAND_INDEX_3 : 98,
    LEFT_HAND_INDEX_4 : 99,
    LEFT_HAND_MIDDLE_1 : 100,
    LEFT_HAND_MIDDLE_2 : 101,
    LEFT_HAND_MIDDLE_3 : 102,
    LEFT_HAND_MIDDLE_4 : 103,
    LEFT_HAND_RING_1 : 104,
    LEFT_HAND_RING_2 : 105,
    LEFT_HAND_RING_3 : 106,
    LEFT_HAND_RING_4 : 107,
    LEFT_HAND_PINKY_1 : 108,
    LEFT_HAND_PINKY_2 : 109,
    LEFT_HAND_PINKY_3 : 110,
    LEFT_HAND_PINKY_4 : 111,
    RIGHT_HAND_START : 112,
    RIGHT_HAND_END : 132,
    RIGHT_HAND_WRIST : 112,
    RIGHT_HAND_THUMB_1 : 113,
    RIGHT_HAND_THUMB_2 : 114,
    RIGHT_HAND_THUMB_3 : 115,
    RIGHT_HAND_THUMB_4 : 116,
    RIGHT_HAND_INDEX_1 : 117,
    RIGHT_HAND_INDEX_2 : 118,
    RIGHT_HAND_INDEX_3: 119,
    RIGHT_HAND_INDEX_4 : 120,
    RIGHT_HAND_MIDDLE_1 : 121,
    RIGHT_HAND_MIDDLE_2 : 122,
    RIGHT_HAND_MIDDLE_3 : 123,
    RIGHT_HAND_MIDDLE_4 : 124,
    RIGHT_HAND_RING_1 : 125,
    RIGHT_HAND_RING_2 : 126,
    RIGHT_HAND_RING_3 : 127,
    RIGHT_HAND_RING_4 : 128,
    RIGHT_HAND_PINKY_1 : 129,
    RIGHT_HAND_PINKY_2 : 130,
    RIGHT_HAND_PINKY_3 : 131,
    RIGHT_HAND_PINKY_4 : 132
  }