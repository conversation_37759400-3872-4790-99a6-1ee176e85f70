/**
 * 基于 ShoulderTouchLogic 的训练会话管理
 * 使用您提供的简化逻辑实现摸肩膀动作的实时检测
 */
import { ref, computed } from 'vue'
import ShoulderTouchLogic from '@/composables/gemini/ShoulderTouchLogic'
import { useAudioFeedback } from './useAudioFeedback'
import { useStateTransition } from './useStateTransition'
import { useTrainingStore } from '@/stores/training'
import { useConnectionStore } from '@/stores/connection'
import { useNotificationStore } from '@/stores/notification'

export function useSimplifiedTrainingSession() {
  // ShoulderTouchLogic 实例
  let shoulderTouchLogic = null
  
  // 保留必要的反馈系统
  const audioFeedback = useAudioFeedback()
  const stateTransition = useStateTransition()

  // 状态管理
  const trainingStore = useTrainingStore()
  const connectionStore = useConnectionStore()
  const notificationStore = useNotificationStore()

  // 训练会话状态
  const isTrainingActive = ref(false)
  const detectionInterval = ref(null)
  const currentProgress = ref(0)
  const currentFeedback = ref('')
  const lastDetectionTime = ref(0)

  // 检测配置
  const DETECTION_CONFIG = {
    sampleInterval: 100, // 检测间隔（毫秒）- 10FPS，平衡性能和响应性
    completionThreshold: 95, // 完成阈值
    holdDuration: 1000 // 完成后保持时间
  }

  // 计算属性
  const currentActionType = computed(() => 'shoulder_touch') // 目前只支持摸肩膀
  const currentActionSide = computed(() => trainingStore.currentAction?.side || 'left')
  const currentActionLevel = computed(() => trainingStore.currentAction?.level || 'medium')
  const isActionInProgress = computed(() => isTrainingActive.value)

  /**
   * 开始训练会话
   */
  const startTrainingSession = () => {
    if (isTrainingActive.value) return

    console.log('[ShoulderTouchSession] 开始摸肩膀训练会话')
    console.log('[ShoulderTouchSession] 训练侧别:', currentActionSide.value, '难度:', currentActionLevel.value)

    // 初始化 ShoulderTouchLogic 实例
    shoulderTouchLogic = new ShoulderTouchLogic(currentActionSide.value, currentActionLevel.value)

    isTrainingActive.value = true
    resetSessionState()
    startRealtimeDetection()
  }

  /**
   * 停止训练会话
   */
  const stopTrainingSession = () => {
    if (!isTrainingActive.value) return

    console.log('[ShoulderTouchSession] 停止摸肩膀训练会话')
    isTrainingActive.value = false

    // 停止检测间隔
    if (detectionInterval.value) {
      clearInterval(detectionInterval.value)
      detectionInterval.value = null
    }

    // 停止音频
    audioFeedback.stopAllAudio()

    // 重置 ShoulderTouchLogic 实例
    shoulderTouchLogic = null

    // 重置状态
    resetSessionState()
  }

  /**
   * 开始实时动作检测
   */
  const startRealtimeDetection = () => {
    console.log('[ShoulderTouchSession] 启动实时检测')

    detectionInterval.value = setInterval(() => {
      if (!isTrainingActive.value || !shoulderTouchLogic) {
        clearInterval(detectionInterval.value)
        detectionInterval.value = null
        return
      }

      // 获取归一化关键点坐标
      const normalizedKeypoints = connectionStore.poseKeypoints

      if (!normalizedKeypoints || normalizedKeypoints.length === 0) {
        return
      }

      // 基本验证关键点数据
      if (normalizedKeypoints.length < 133) {
        console.log('[ShoulderTouchSession] 关键点数据不完整:', normalizedKeypoints.length)
        return
      }

      // 使用 ShoulderTouchLogic 进行检测
      const result = shoulderTouchLogic.update(normalizedKeypoints)

      // 处理检测结果
      handleDetectionResult(result)

    }, DETECTION_CONFIG.sampleInterval)
  }

  /**
   * 处理检测结果
   */
  const handleDetectionResult = (result) => {
    const { state, score, feedback } = result

    // 更新当前状态
    currentProgress.value = score
    currentFeedback.value = feedback
    lastDetectionTime.value = Date.now()

    console.log('[ShoulderTouchSession] 检测结果:', {
      state,
      score,
      feedback,
      side: currentActionSide.value
    })

    // 播放音频反馈
    handleAudioFeedback(result)

    // 检查动作完成
    if (state === 'COMPLETED') {
      handleActionCompleted(result)
    }
  }

  /**
   * 处理音频反馈
   */
  const handleAudioFeedback = (result) => {
    const { state } = result

    if (state === 'COMPLETED') {
      audioFeedback.playCelebrationAudio('动作完成！')
    } else if (state === 'HOLDING') {
      audioFeedback.playEncouragementAudio('很好！继续保持')
    } else if (state === 'MOVING_TO_TARGET') {
      audioFeedback.playEncouragementAudio('做得不错！')
    }
  }

  /**
   * 处理动作完成
   */
  const handleActionCompleted = (result) => {
    console.log('[ShoulderTouchSession] 动作完成:', result)

    // 播放完成音效
    audioFeedback.playCelebrationAudio('太棒了！动作完成')

    // 显示完成反馈
    notificationStore.showSuccess(result.feedback)

    // 触发状态转换
    setTimeout(() => {
      stateTransition.handleActionComplete(result.score)
    }, 1000)
  }

  /**
   * 重置会话状态
   */
  const resetSessionState = () => {
    currentProgress.value = 0
    currentFeedback.value = ''
    lastDetectionTime.value = 0

    // 重置 ShoulderTouchLogic 实例
    if (shoulderTouchLogic) {
      shoulderTouchLogic.reset()
    }

    console.log('[ShoulderTouchSession] 会话状态已重置')
  }

  /**
   * 获取 ShoulderTouchLogic 状态（用于调试）
   */
  const getDetectorState = () => {
    if (!shoulderTouchLogic) return {}
    return {
      state: shoulderTouchLogic.state,
      score: shoulderTouchLogic.score,
      feedback: shoulderTouchLogic.feedback
    }
  }

  return {
    // 响应式数据
    isTrainingActive,
    currentProgress,
    currentFeedback,
    lastDetectionTime,

    // 计算属性
    currentActionType,
    currentActionSide,
    currentActionLevel,
    isActionInProgress,

    // 核心方法
    startTrainingSession,
    stopTrainingSession,
    resetSessionState,

    // 调试方法
    getDetectorState,

    // ShoulderTouchLogic 访问
    shoulderTouchLogic: () => shoulderTouchLogic
  }
}
