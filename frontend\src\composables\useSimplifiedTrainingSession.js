/**
 * 简化版训练会话管理
 * 使用简化的动作检测器，专注于核心功能
 */
import { ref, computed } from 'vue'
import { useSimplifiedActionDetector } from './simplified/simplifiedActionDetector'
import { useAudioFeedback } from './useAudioFeedback'
import { useStateTransition } from './useStateTransition'
import { useTrainingStore } from '@/stores/training'
import { useConnectionStore } from '@/stores/connection'
import { useNotificationStore } from '@/stores/notification'

export function useSimplifiedTrainingSession() {
  // 简化的检测器
  const actionDetector = useSimplifiedActionDetector()
  
  // 保留必要的反馈系统
  const audioFeedback = useAudioFeedback()
  const stateTransition = useStateTransition()

  // 状态管理
  const trainingStore = useTrainingStore()
  const connectionStore = useConnectionStore()
  const notificationStore = useNotificationStore()

  // 训练会话状态
  const isTrainingActive = ref(false)
  const detectionInterval = ref(null)
  const currentProgress = ref(0)
  const currentFeedback = ref('')
  const lastDetectionTime = ref(0)
  const actionCompleted = ref(false)  // 防止重复完成

  // 检测配置
  const DETECTION_CONFIG = {
    sampleInterval: 100, // 检测间隔（毫秒）- 10FPS，平衡性能和响应性
    completionThreshold: 95, // 完成阈值
    holdDuration: 1000 // 完成后保持时间
  }

  // 计算属性
  const currentActionType = computed(() => trainingStore.currentAction?.action_type)
  const currentActionSide = computed(() => trainingStore.currentAction?.side || 'left')
  const isActionInProgress = computed(() => isTrainingActive.value && !!currentActionType.value)

  /**
   * 开始训练会话
   */
  const startTrainingSession = () => {
    if (isTrainingActive.value) return

    console.log('[SimplifiedTrainingSession] 开始简化训练会话')
    console.log('[SimplifiedTrainingSession] 当前动作:', currentActionType.value, currentActionSide.value)

    isTrainingActive.value = true
    resetSessionState()
    startRealtimeDetection()
  }

  /**
   * 停止训练会话
   */
  const stopTrainingSession = () => {
    if (!isTrainingActive.value) return

    console.log('[SimplifiedTrainingSession] 停止简化训练会话')
    isTrainingActive.value = false

    // 停止检测间隔
    if (detectionInterval.value) {
      clearInterval(detectionInterval.value)
      detectionInterval.value = null
    }

    // 停止音频
    audioFeedback.stopAllAudio()
    
    // 重置状态
    resetSessionState()
  }

  /**
   * 开始实时动作检测
   */
  const startRealtimeDetection = () => {
    console.log('[SimplifiedTrainingSession] 启动实时检测')

    detectionInterval.value = setInterval(() => {
      if (!isTrainingActive.value || !currentActionType.value) {
        clearInterval(detectionInterval.value)
        detectionInterval.value = null
        return
      }

      // 获取归一化关键点坐标
      const normalizedKeypoints = connectionStore.poseKeypoints

      if (!normalizedKeypoints || normalizedKeypoints.length === 0) {
        return
      }

      // 使用引擎验证关键点数据
      const validation = actionDetector.shoulderTouchDetector.engine.validateKeypointsData(normalizedKeypoints)
      if (!validation.isValid) {
        console.log('[SimplifiedTrainingSession] 关键点验证失败:', validation.reason)
        return
      }

      // 执行简化检测
      const detectionResult = actionDetector.detectAction(
        currentActionType.value,
        normalizedKeypoints,
        currentActionSide.value
      )

      // 处理检测结果
      handleDetectionResult(detectionResult)

    }, DETECTION_CONFIG.sampleInterval)
  }

  /**
   * 处理检测结果
   */
  const handleDetectionResult = (result) => {
    const { isCompleted, progress, feedback } = result

    // 更新当前状态
    currentProgress.value = progress
    currentFeedback.value = feedback
    lastDetectionTime.value = Date.now()

    console.log('[SimplifiedTrainingSession] 检测结果:', {
      action: currentActionType.value,
      side: currentActionSide.value,
      progress,
      isCompleted,
      feedback,
      alreadyCompleted: actionCompleted.value
    })

    // 播放音频反馈
    handleAudioFeedback(result)

    // 检查动作完成（防止重复触发）
    if (isCompleted && !actionCompleted.value) {
      actionCompleted.value = true
      handleActionCompleted(result)
    }
  }

  /**
   * 处理音频反馈
   */
  const handleAudioFeedback = (result) => {
    const { progress, isCompleted } = result

    if (isCompleted) {
      audioFeedback.playCelebrationAudio('动作完成！')
    } else if (progress >= 80) {
      audioFeedback.playEncouragementAudio('很好！继续保持')
    } else if (progress >= 50) {
      audioFeedback.playEncouragementAudio('做得不错！')
    }
  }

  /**
   * 处理动作完成
   */
  const handleActionCompleted = (result) => {
    console.log('[SimplifiedTrainingSession] 动作完成:', result)

    // 播放完成音效
    audioFeedback.playCelebrationAudio('太棒了！动作完成')

    // 显示完成反馈
    notificationStore.showSuccess(result.feedback)

    // 重置检测器状态
    actionDetector.resetDetector(currentActionType.value)

    // 延迟后触发状态转换
    setTimeout(() => {
      stateTransition.handleActionComplete(100)
    }, DETECTION_CONFIG.holdDuration)
  }

  /**
   * 重置会话状态
   */
  const resetSessionState = () => {
    currentProgress.value = 0
    currentFeedback.value = ''
    lastDetectionTime.value = 0
    actionCompleted.value = false  // 重置完成标志

    // 重置所有检测器
    actionDetector.resetAllDetectors()

    console.log('[SimplifiedTrainingSession] 会话状态已重置')
  }

  /**
   * 获取检测器状态（用于调试）
   */
  const getDetectorState = () => {
    if (!currentActionType.value) return {}
    return actionDetector.getDetectorState(currentActionType.value)
  }

  return {
    // 响应式数据
    isTrainingActive,
    currentProgress,
    currentFeedback,
    lastDetectionTime,

    // 计算属性
    currentActionType,
    currentActionSide,
    isActionInProgress,

    // 核心方法
    startTrainingSession,
    stopTrainingSession,
    resetSessionState,

    // 调试方法
    getDetectorState,

    // 检测器访问
    actionDetector
  }
}
