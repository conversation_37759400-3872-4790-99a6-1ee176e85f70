/**
 * 几何计算工具函数
 * 用于姿态检测中的距离、角度等计算
 */

/**
 * 计算两点之间的欧几里得距离
 * @param {Object} point1 - 第一个点 {x, y} 或 [x, y, confidence]
 * @param {Object} point2 - 第二个点 {x, y} 或 [x, y, confidence]
 * @returns {number} - 两点间的距离
 */
export function calculateDistance(point1, point2) {
  // 处理数组格式的点
  const x1 = Array.isArray(point1) ? point1[0] : point1.x
  const y1 = Array.isArray(point1) ? point1[1] : point1.y
  const x2 = Array.isArray(point2) ? point2[0] : point2.x
  const y2 = Array.isArray(point2) ? point2[1] : point2.y

  const dx = x2 - x1
  const dy = y2 - y1
  
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * 计算三点形成的角度（以中间点为顶点）
 * @param {Object} point1 - 第一个点
 * @param {Object} vertex - 顶点
 * @param {Object} point3 - 第三个点
 * @returns {number} - 角度（度）
 */
export function calculateAngle(point1, vertex, point3) {
  // 处理数组格式的点
  const x1 = Array.isArray(point1) ? point1[0] : point1.x
  const y1 = Array.isArray(point1) ? point1[1] : point1.y
  const vx = Array.isArray(vertex) ? vertex[0] : vertex.x
  const vy = Array.isArray(vertex) ? vertex[1] : vertex.y
  const x3 = Array.isArray(point3) ? point3[0] : point3.x
  const y3 = Array.isArray(point3) ? point3[1] : point3.y

  const vector1 = { x: x1 - vx, y: y1 - vy }
  const vector2 = { x: x3 - vx, y: y3 - vy }

  const dot = vector1.x * vector2.x + vector1.y * vector2.y
  const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y)
  const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y)

  if (mag1 === 0 || mag2 === 0) return 0

  const cosAngle = dot / (mag1 * mag2)
  return Math.acos(Math.max(-1, Math.min(1, cosAngle))) * (180 / Math.PI)
}

/**
 * 检查关键点是否有效
 * @param {Array|Object} point - 关键点
 * @param {number} confidenceThreshold - 置信度阈值
 * @returns {boolean} - 是否有效
 */
export function isValidKeypoint(point, confidenceThreshold = 0.5) {
  if (!point) return false
  
  if (Array.isArray(point)) {
    return point.length >= 3 && 
           typeof point[0] === 'number' && 
           typeof point[1] === 'number' && 
           point[2] >= confidenceThreshold
  }
  
  return typeof point.x === 'number' && 
         typeof point.y === 'number' && 
         (point.confidence || point.c || 1) >= confidenceThreshold
}

/**
 * 将点转换为标准格式 {x, y, confidence}
 * @param {Array|Object} point - 输入点
 * @returns {Object} - 标准格式的点
 */
export function normalizePoint(point) {
  if (Array.isArray(point)) {
    return {
      x: point[0],
      y: point[1],
      confidence: point[2] || 1
    }
  }
  
  return {
    x: point.x,
    y: point.y,
    confidence: point.confidence || point.c || 1
  }
}

// 创建全局 utils 对象以兼容 ShoulderTouchLogic
export const utils = {
  calculateDistance,
  calculateAngle,
  isValidKeypoint,
  normalizePoint
}

// 默认导出
export default utils
