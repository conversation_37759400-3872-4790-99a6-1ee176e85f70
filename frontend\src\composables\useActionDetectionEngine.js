/**
 * 动作检测核心引擎 - 简化版
 * 专注于归一化坐标的关键点验证和基础几何计算工具
 */
import { ref } from 'vue'
import { KeyPointMapping } from '@/utils/poseConstants'

export function useActionDetectionEngine() {
  // 画面完整性状态
  const visibilityStatus = ref({
    isComplete: true,
    missingParts: [],
    message: ''
  })

  /**
   * 检查归一化关键点是否有效
   * @param {Object|Array} point - 关键点对象 {x, y, confidence} 或数组 [x, y, confidence]
   * @returns {boolean} - 是否有效
   */
  const isValidKeypoint = (point) => {
    if (!point) return false

    let x, y, confidence

    // 支持数组格式 [x, y, confidence] 和对象格式 {x, y, confidence}
    if (Array.isArray(point)) {
      if (point.length < 3) return false
      x = point[0]
      y = point[1]
      confidence = point[2]
    } else {
      x = point.x
      y = point.y
      confidence = point.confidence
    }

    // 检查坐标是否为无效值
    if (x === undefined || y === undefined) return false
    if (x === 0 && y === 0) return false

    // 检查是否在归一化范围内 (0-1)，边界值认为无效
    if (x <= 0 || x >= 1) return false
    if (y <= 0 || y >= 1) return false

    // 检查置信度
    if (confidence !== undefined && confidence < 0.3) return false

    return true
  }

  /**
   * 检查归一化关键点是否在安全边界内
   * @param {Object|Array} point - 关键点对象 {x, y, confidence} 或 [x, y, confidence]
   * @param {number} margin - 边界容忍度 (归一化值，默认0.05)
   * @returns {boolean} - 是否在边界内
   */
  const isPointInBounds = (point, margin = 0.05) => {
    if (!isValidKeypoint(point)) return false

    // 获取坐标值，支持数组和对象格式
    const x = Array.isArray(point) ? point[0] : point.x
    const y = Array.isArray(point) ? point[1] : point.y

    // 归一化坐标边界检查（考虑边距）
    return x >= margin &&
           x <= 1 - margin &&
           y >= margin &&
           y <= 1 - margin
  }

  /**
   * 获取身体部位的友好名称
   * @param {number} pointIndex - 关键点索引
   * @param {string} side - 动作侧面 ('left' | 'right')
   * @returns {string} - 部位名称
   */
  const getBodyPartName = (pointIndex, side) => {
    const mapping = {
      [KeyPointMapping.LEFT_WRIST]: '左手',
      [KeyPointMapping.RIGHT_WRIST]: '右手',
      [KeyPointMapping.LEFT_SHOULDER]: '左肩',
      [KeyPointMapping.RIGHT_SHOULDER]: '右肩',
      [KeyPointMapping.LEFT_ELBOW]: '左肘',
      [KeyPointMapping.RIGHT_ELBOW]: '右肘',
      [KeyPointMapping.NOSE]: '头部',
      [KeyPointMapping.LEFT_HAND_WRIST]: '左手腕',
      [KeyPointMapping.RIGHT_HAND_WRIST]: '右手腕',
      [KeyPointMapping.LEFT_HAND_THUMB_4]: '左手拇指',
      [KeyPointMapping.RIGHT_HAND_THUMB_4]: '右手拇指',
      [KeyPointMapping.LEFT_HAND_INDEX_4]: '左手食指',
      [KeyPointMapping.RIGHT_HAND_INDEX_4]: '右手食指'
    }
    
    return mapping[pointIndex] || `${side === 'left' ? '左' : '右'}侧关键部位`
  }

  /**
   * 检查动作所需关键点的完整性（归一化坐标）
   * @param {Array} keypoints - 归一化关键点数组
   * @param {Array} requiredPoints - 所需关键点索引数组
   * @param {string} side - 动作侧面
   * @returns {Object} - 检查结果 {isComplete, missingParts, message}
   */
  const checkRequiredKeypoints = (keypoints, requiredPoints, side) => {
    const missingParts = []
    for (const pointIndex of requiredPoints) {
      const point = keypoints[pointIndex]
      // 检查关键点是否有效且在画面内
      if (!isValidKeypoint(point) || !isPointInBounds(point)) {
        const partName = getBodyPartName(pointIndex, side)
        if (!missingParts.includes(partName)) {
          missingParts.push(partName)
        }
      }
    }

    const isComplete = missingParts.length === 0
    let message = ''

    if (!isComplete) {
      message = `请保持${missingParts.join('、')}在画面完整展示`
    }

    const result = {
      isComplete,
      missingParts,
      message
    }

    // 更新全局状态
    visibilityStatus.value = result

    return result
  }

  /**
   * 计算两个归一化点之间的距离
   * @param {Object|Array} point1 - 第一个点 {x, y, confidence} 或 [x, y, confidence]
   * @param {Object|Array} point2 - 第二个点 {x, y, confidence} 或 [x, y, confidence]
   * @returns {number} - 归一化距离值，无效点返回Infinity
   */
  const calculateDistance = (point1, point2) => {
    if (!isValidKeypoint(point1) || !isValidKeypoint(point2)) {
      return Infinity
    }

    // 获取坐标值，支持数组和对象格式
    const x1 = Array.isArray(point1) ? point1[0] : point1.x
    const y1 = Array.isArray(point1) ? point1[1] : point1.y
    const x2 = Array.isArray(point2) ? point2[0] : point2.x
    const y2 = Array.isArray(point2) ? point2[1] : point2.y

    const dx = x1 - x2
    const dy = y1 - y2
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 计算三个归一化点形成的角度
   * @param {Object|Array} point1 - 第一个点 {x, y, confidence} 或 [x, y, confidence]
   * @param {Object|Array} vertex - 顶点 {x, y, confidence} 或 [x, y, confidence]
   * @param {Object|Array} point3 - 第三个点 {x, y, confidence} 或 [x, y, confidence]
   * @returns {number} - 角度值（度），无效点返回0
   */
  const calculateAngle = (point1, vertex, point3) => {
    if (!isValidKeypoint(point1) || !isValidKeypoint(vertex) || !isValidKeypoint(point3)) {
      return 0
    }

    // 获取坐标值，支持数组和对象格式
    const x1 = Array.isArray(point1) ? point1[0] : point1.x
    const y1 = Array.isArray(point1) ? point1[1] : point1.y
    const vx = Array.isArray(vertex) ? vertex[0] : vertex.x
    const vy = Array.isArray(vertex) ? vertex[1] : vertex.y
    const x3 = Array.isArray(point3) ? point3[0] : point3.x
    const y3 = Array.isArray(point3) ? point3[1] : point3.y

    const vector1 = { x: x1 - vx, y: y1 - vy }
    const vector2 = { x: x3 - vx, y: y3 - vy }

    const dot = vector1.x * vector2.x + vector1.y * vector2.y
    const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y)
    const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y)

    if (mag1 === 0 || mag2 === 0) return 0

    const cosAngle = dot / (mag1 * mag2)
    return Math.acos(Math.max(-1, Math.min(1, cosAngle))) * (180 / Math.PI)
  }

  /**
   * 计算向量的角度（相对于水平线）
   * @param {Object|Array} point1 - 起点 {x, y, confidence} 或 [x, y, confidence]
   * @param {Object|Array} point2 - 终点 {x, y, confidence} 或 [x, y, confidence]
   * @returns {number} - 角度值（度），无效点返回0
   */
  const calculateVectorAngle = (point1, point2) => {
    if (!isValidKeypoint(point1) || !isValidKeypoint(point2)) {
      return 0
    }

    // 获取坐标值，支持数组和对象格式
    const x1 = Array.isArray(point1) ? point1[0] : point1.x
    const y1 = Array.isArray(point1) ? point1[1] : point1.y
    const x2 = Array.isArray(point2) ? point2[0] : point2.x
    const y2 = Array.isArray(point2) ? point2[1] : point2.y

    const dx = x2 - x1
    const dy = y2 - y1

    return Math.atan2(dy, dx) * (180 / Math.PI)
  }

  /**
   * 基于肩膀宽度标准化距离
   * @param {number} distance - 原始归一化距离
   * @param {Array} keypoints - 归一化关键点数组
   * @returns {number} - 标准化距离，无法计算返回Infinity
   */
  const normalizeDistance = (distance, keypoints) => {
    const leftShoulder = keypoints[KeyPointMapping.LEFT_SHOULDER]
    const rightShoulder = keypoints[KeyPointMapping.RIGHT_SHOULDER]

    if (!isValidKeypoint(leftShoulder) || !isValidKeypoint(rightShoulder)) {
      return Infinity
    }

    const shoulderWidth = calculateDistance(leftShoulder, rightShoulder)
    if (shoulderWidth === 0 || shoulderWidth === Infinity) {
      return Infinity
    }

    return distance / shoulderWidth
  }

  return {
    // 响应式数据
    visibilityStatus,
    
    // 关键点验证
    isValidKeypoint,
    isPointInBounds,
    checkRequiredKeypoints,
    
    // 几何计算
    calculateDistance,
    calculateAngle,
    calculateVectorAngle,
    normalizeDistance,
    
    // 工具方法
    getBodyPartName
  }
}

