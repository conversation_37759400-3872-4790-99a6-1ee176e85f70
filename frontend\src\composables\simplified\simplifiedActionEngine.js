/**
 * 简化动作检测引擎
 * 提供基础的几何计算工具，专注于归一化坐标的直接判断
 */
import { KeyPointMapping } from '@/utils/poseConstants'

export function useSimplifiedActionEngine() {
  
  /**
   * 检查归一化关键点是否有效
   * @param {Object|Array} point - 关键点 {x, y, confidence} 或 [x, y, confidence]
   * @returns {boolean} - 是否有效
   */
  const isValidKeypoint = (point) => {
    if (!point) return false

    let x, y, confidence
    if (Array.isArray(point)) {
      if (point.length < 3) return false
      x = point[0]
      y = point[1] 
      confidence = point[2]
    } else {
      x = point.x
      y = point.y
      confidence = point.confidence
    }

    // 检查坐标有效性
    if (x === undefined || y === undefined) return false
    if (x === 0 && y === 0) return false
    if (x <= 0 || x >= 1 || y <= 0 || y >= 1) return false
    if (confidence !== undefined && confidence < 0.3) return false

    return true
  }

  /**
   * 计算两个归一化点之间的距离
   * @param {Object|Array} point1 - 第一个点
   * @param {Object|Array} point2 - 第二个点  
   * @returns {number} - 归一化距离，无效返回Infinity
   */
  const calculateDistance = (point1, point2) => {
    if (!isValidKeypoint(point1) || !isValidKeypoint(point2)) {
      return Infinity
    }

    const x1 = Array.isArray(point1) ? point1[0] : point1.x
    const y1 = Array.isArray(point1) ? point1[1] : point1.y
    const x2 = Array.isArray(point2) ? point2[0] : point2.x
    const y2 = Array.isArray(point2) ? point2[1] : point2.y

    const dx = x1 - x2
    const dy = y1 - y2
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 计算三个点形成的角度
   * @param {Object|Array} point1 - 第一个点
   * @param {Object|Array} vertex - 顶点
   * @param {Object|Array} point3 - 第三个点
   * @returns {number} - 角度值（度），无效返回0
   */
  const calculateAngle = (point1, vertex, point3) => {
    if (!isValidKeypoint(point1) || !isValidKeypoint(vertex) || !isValidKeypoint(point3)) {
      return 0
    }

    const x1 = Array.isArray(point1) ? point1[0] : point1.x
    const y1 = Array.isArray(point1) ? point1[1] : point1.y
    const vx = Array.isArray(vertex) ? vertex[0] : vertex.x
    const vy = Array.isArray(vertex) ? vertex[1] : vertex.y
    const x3 = Array.isArray(point3) ? point3[0] : point3.x
    const y3 = Array.isArray(point3) ? point3[1] : point3.y

    const vector1 = { x: x1 - vx, y: y1 - vy }
    const vector2 = { x: x3 - vx, y: y3 - vy }

    const dot = vector1.x * vector2.x + vector1.y * vector2.y
    const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y)
    const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y)

    if (mag1 === 0 || mag2 === 0) return 0

    const cosAngle = dot / (mag1 * mag2)
    return Math.acos(Math.max(-1, Math.min(1, cosAngle))) * (180 / Math.PI)
  }

  /**
   * 检查所需关键点是否都可用
   * @param {Array} keypoints - 关键点数组
   * @param {Array} requiredIndices - 所需关键点索引
   * @returns {boolean} - 是否都可用
   */
  const checkRequiredKeypoints = (keypoints, requiredIndices) => {
    return requiredIndices.every(index => isValidKeypoint(keypoints[index]))
  }

  /**
   * 获取关键点坐标
   * @param {Object|Array} point - 关键点
   * @returns {Object} - {x, y} 坐标对象
   */
  const getCoords = (point) => {
    if (Array.isArray(point)) {
      return { x: point[0], y: point[1] }
    }
    return { x: point.x, y: point.y }
  }

  return {
    // 关键点验证
    isValidKeypoint,
    checkRequiredKeypoints,
    
    // 几何计算
    calculateDistance,
    calculateAngle,
    
    // 工具方法
    getCoords,
    
    // 关键点映射
    KeyPointMapping
  }
}
