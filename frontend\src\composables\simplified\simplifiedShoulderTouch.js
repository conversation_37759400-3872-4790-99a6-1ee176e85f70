/**
 * 简化摸肩膀检测器
 * 仅判断手掌中心与对侧肩膀的距离
 */
import { useSimplifiedActionEngine } from './simplifiedActionEngine'

export function useSimplifiedShoulderTouch() {
  const engine = useSimplifiedActionEngine()
  const { KeyPointMapping } = engine

  // 触摸距离阈值（归一化坐标）- 调整为更严格的值
  const TOUCH_THRESHOLD = 0.08
  const APPROACH_THRESHOLD = 0.15

  /**
   * 检测摸肩膀动作
   * @param {Array} keypoints - 归一化关键点数组 [x, y, confidence]
   * @param {string} side - 动作侧别 ('left' | 'right')
   * @returns {Object} - 检测结果 {isCompleted, progress, feedback}
   */
  const detectShoulderTouch = (keypoints, side = 'left') => {
    // 确定手腕和目标肩膀的索引
    const wristIndex = side === 'left' ? KeyPointMapping.LEFT_WRIST : KeyPointMapping.RIGHT_WRIST
    const targetShoulderIndex = side === 'left' ? KeyPointMapping.RIGHT_SHOULDER : KeyPointMapping.LEFT_SHOULDER
    
    // 检查所需关键点是否可用
    const requiredIndices = [wristIndex, targetShoulderIndex]
    if (!engine.checkRequiredKeypoints(keypoints, requiredIndices)) {
      return {
        isCompleted: false,
        progress: 0,
        feedback: '请保持手部和肩膀在画面内'
      }
    }

    // 获取关键点
    const wrist = keypoints[wristIndex]
    const targetShoulder = keypoints[targetShoulderIndex]

    // 计算手腕到对侧肩膀的距离
    const distance = engine.calculateDistance(wrist, targetShoulder)

    if (distance === Infinity) {
      return {
        isCompleted: false,
        progress: 0,
        feedback: '无法检测到手部位置'
      }
    }

    // 调试信息
    console.log(`[ShoulderTouch] ${side}手摸肩膀检测:`, {
      wrist: [wrist[0].toFixed(3), wrist[1].toFixed(3)],
      targetShoulder: [targetShoulder[0].toFixed(3), targetShoulder[1].toFixed(3)],
      distance: distance.toFixed(3),
      threshold: TOUCH_THRESHOLD
    })

    // 额外验证：检查关键点是否在合理范围内
    if (wrist[0] < 0 || wrist[0] > 1 || wrist[1] < 0 || wrist[1] > 1 ||
        targetShoulder[0] < 0 || targetShoulder[0] > 1 || targetShoulder[1] < 0 || targetShoulder[1] > 1) {
      return {
        isCompleted: false,
        progress: 0,
        feedback: '关键点数据异常，请重新调整姿势'
      }
    }

    // 判断是否完成触摸
    if (distance <= TOUCH_THRESHOLD) {
      return {
        isCompleted: true,
        progress: 100,
        feedback: `${side === 'left' ? '左手' : '右手'}成功触摸${side === 'left' ? '右肩' : '左肩'}！`
      }
    }

    // 计算进度
    let progress = 0
    let feedback = ''
    
    if (distance <= APPROACH_THRESHOLD) {
      // 接近阶段，计算进度
      progress = Math.round((1 - (distance - TOUCH_THRESHOLD) / (APPROACH_THRESHOLD - TOUCH_THRESHOLD)) * 80)
      feedback = `继续将${side === 'left' ? '左手' : '右手'}向${side === 'left' ? '右肩' : '左肩'}移动`
    } else {
      // 距离较远
      progress = Math.max(0, Math.round((1 - distance / 0.5) * 30))
      feedback = `请将${side === 'left' ? '左手' : '右手'}向${side === 'left' ? '右肩' : '左肩'}移动`
    }

    return {
      isCompleted: false,
      progress: Math.max(0, Math.min(99, progress)),
      feedback
    }
  }

  /**
   * 获取所需关键点索引
   * @param {string} side - 动作侧别
   * @returns {Array} - 关键点索引数组
   */
  const getRequiredKeypoints = (side) => {
    const wristIndex = side === 'left' ? KeyPointMapping.LEFT_WRIST : KeyPointMapping.RIGHT_WRIST
    const targetShoulderIndex = side === 'left' ? KeyPointMapping.RIGHT_SHOULDER : KeyPointMapping.LEFT_SHOULDER
    return [wristIndex, targetShoulderIndex]
  }

  return {
    detectShoulderTouch,
    getRequiredKeypoints,
    TOUCH_THRESHOLD,
    APPROACH_THRESHOLD
  }
}
