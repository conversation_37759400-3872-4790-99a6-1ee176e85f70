/**
 * 简化举手臂检测器
 * 仅判断肘关节角度是否达到区间范围以及肘部是否在肩膀上方
 */
import { useSimplifiedActionEngine } from './simplifiedActionEngine'

export function useSimplifiedArmRaise() {
  const engine = useSimplifiedActionEngine()
  const { KeyPointMapping } = engine

  // 角度阈值
  const MIN_ANGLE = 140  // 最小肘关节角度
  const PERFECT_ANGLE = 160  // 理想肘关节角度

  /**
   * 检测举手臂动作
   * @param {Array} keypoints - 归一化关键点数组 [x, y, confidence]
   * @param {string} side - 动作侧别 ('left' | 'right')
   * @returns {Object} - 检测结果 {isCompleted, progress, feedback}
   */
  const detectArmRaise = (keypoints, side = 'left') => {
    // 确定关键点索引
    const wristIndex = side === 'left' ? KeyPointMapping.LEFT_WRIST : KeyPointMapping.RIGHT_WRIST
    const elbowIndex = side === 'left' ? KeyPointMapping.LEFT_ELBOW : KeyPointMapping.RIGHT_ELBOW
    const shoulderIndex = side === 'left' ? KeyPointMapping.LEFT_SHOULDER : KeyPointMapping.RIGHT_SHOULDER
    
    // 检查所需关键点是否可用
    const requiredIndices = [wristIndex, elbowIndex, shoulderIndex]
    if (!engine.checkRequiredKeypoints(keypoints, requiredIndices)) {
      return {
        isCompleted: false,
        progress: 0,
        feedback: '请保持手臂完全在画面内'
      }
    }

    // 获取关键点
    const wrist = keypoints[wristIndex]
    const elbow = keypoints[elbowIndex]
    const shoulder = keypoints[shoulderIndex]

    // 计算肘关节角度（手腕-肘部-肩膀）
    const elbowAngle = engine.calculateAngle(wrist, elbow, shoulder)
    
    if (elbowAngle === 0) {
      return {
        isCompleted: false,
        progress: 0,
        feedback: '无法计算手臂角度'
      }
    }

    // 检查肘部是否在肩膀上方（y坐标更小）
    const elbowCoords = engine.getCoords(elbow)
    const shoulderCoords = engine.getCoords(shoulder)
    const isElbowAboveShoulder = elbowCoords.y < shoulderCoords.y

    // 判断是否完成举手臂动作
    if (elbowAngle >= MIN_ANGLE && isElbowAboveShoulder) {
      const angleProgress = Math.min(100, (elbowAngle / PERFECT_ANGLE) * 100)
      return {
        isCompleted: true,
        progress: 100,
        feedback: `${side === 'left' ? '左臂' : '右臂'}举起完成！角度：${Math.round(elbowAngle)}°`
      }
    }

    // 计算进度
    let progress = 0
    let feedback = ''

    // 角度进度（占70%）
    const angleProgress = Math.min(70, (elbowAngle / MIN_ANGLE) * 70)
    
    // 高度进度（占30%）
    const heightProgress = isElbowAboveShoulder ? 30 : 0

    progress = Math.round(angleProgress + heightProgress)

    // 生成反馈
    if (!isElbowAboveShoulder) {
      feedback = `请将${side === 'left' ? '左臂' : '右臂'}抬高`
    } else if (elbowAngle < MIN_ANGLE) {
      feedback = `${side === 'left' ? '左臂' : '右臂'}高度很好，请伸直手臂`
    } else {
      feedback = `继续将${side === 'left' ? '左臂' : '右臂'}举高`
    }

    return {
      isCompleted: false,
      progress: Math.max(0, Math.min(99, progress)),
      feedback
    }
  }

  /**
   * 获取所需关键点索引
   * @param {string} side - 动作侧别
   * @returns {Array} - 关键点索引数组
   */
  const getRequiredKeypoints = (side) => {
    const wristIndex = side === 'left' ? KeyPointMapping.LEFT_WRIST : KeyPointMapping.RIGHT_WRIST
    const elbowIndex = side === 'left' ? KeyPointMapping.LEFT_ELBOW : KeyPointMapping.RIGHT_ELBOW
    const shoulderIndex = side === 'left' ? KeyPointMapping.LEFT_SHOULDER : KeyPointMapping.RIGHT_SHOULDER
    return [wristIndex, elbowIndex, shoulderIndex]
  }

  return {
    detectArmRaise,
    getRequiredKeypoints,
    MIN_ANGLE,
    PERFECT_ANGLE
  }
}
