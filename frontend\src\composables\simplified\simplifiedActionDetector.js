/**
 * 简化动作检测管理器
 * 统一管理所有简化的动作检测器
 */
import { useSimplifiedShoulderTouch } from './simplifiedShoulderTouch'
import { useSimplifiedArmRaise } from './simplifiedArmRaise'
import { useSimplifiedPalmFlip } from './simplifiedPalmFlip'
import { useSimplifiedFingerTouch } from './simplifiedFingerTouch'

export function useSimplifiedActionDetector() {
  // 初始化所有检测器
  const shoulderTouchDetector = useSimplifiedShoulderTouch()
  const armRaiseDetector = useSimplifiedArmRaise()
  const palmFlipDetector = useSimplifiedPalmFlip()
  const fingerTouchDetector = useSimplifiedFingerTouch()

  /**
   * 执行动作检测
   * @param {string} actionType - 动作类型
   * @param {Array} keypoints - 归一化关键点数组
   * @param {string} side - 动作侧别
   * @returns {Object} - 统一的检测结果格式
   */
  const detectAction = (actionType, keypoints, side = 'left') => {
    if (!keypoints || keypoints.length === 0) {
      return {
        isCompleted: false,
        progress: 0,
        feedback: '未检测到姿态数据',
        actionType,
        side
      }
    }

    let result = null

    switch (actionType) {
      case 'shoulder_touch':
        result = shoulderTouchDetector.detectShoulderTouch(keypoints, side)
        break
      case 'arm_raise':
        result = armRaiseDetector.detectArmRaise(keypoints, side)
        break
      case 'palm_flip':
        result = palmFlipDetector.detectPalmFlip(keypoints, side)
        break
      case 'finger_touch':
        result = fingerTouchDetector.detectFingerTouch(keypoints, side)
        break
      default:
        return {
          isCompleted: false,
          progress: 0,
          feedback: `未知动作类型: ${actionType}`,
          actionType,
          side
        }
    }

    // 添加动作类型和侧别信息
    return {
      ...result,
      actionType,
      side
    }
  }

  /**
   * 获取动作所需的关键点
   * @param {string} actionType - 动作类型
   * @param {string} side - 动作侧别
   * @returns {Array} - 关键点索引数组
   */
  const getRequiredKeypoints = (actionType, side) => {
    switch (actionType) {
      case 'shoulder_touch':
        return shoulderTouchDetector.getRequiredKeypoints(side)
      case 'arm_raise':
        return armRaiseDetector.getRequiredKeypoints(side)
      case 'palm_flip':
        return palmFlipDetector.getRequiredKeypoints(side)
      case 'finger_touch':
        return fingerTouchDetector.getRequiredKeypoints(side)
      default:
        return []
    }
  }

  /**
   * 重置所有检测器状态
   */
  const resetAllDetectors = () => {
    palmFlipDetector.resetFlipState()
    fingerTouchDetector.resetTouchState()
    console.log('[SimplifiedActionDetector] 所有检测器状态已重置')
  }

  /**
   * 重置特定检测器状态
   * @param {string} actionType - 动作类型
   */
  const resetDetector = (actionType) => {
    switch (actionType) {
      case 'palm_flip':
        palmFlipDetector.resetFlipState()
        break
      case 'finger_touch':
        fingerTouchDetector.resetTouchState()
        break
      // shoulder_touch 和 arm_raise 无需重置状态
    }
    console.log(`[SimplifiedActionDetector] ${actionType} 检测器状态已重置`)
  }

  /**
   * 获取检测器状态信息（用于调试）
   * @param {string} actionType - 动作类型
   * @returns {Object} - 状态信息
   */
  const getDetectorState = (actionType) => {
    switch (actionType) {
      case 'palm_flip':
        return {
          flipCount: palmFlipDetector.flipState.value.flipCount,
          targetFlips: palmFlipDetector.TARGET_FLIPS
        }
      case 'finger_touch':
        return {
          completedTouches: fingerTouchDetector.touchState.value.completedTouches.length,
          currentTarget: fingerTouchDetector.touchState.value.currentTarget,
          isHolding: fingerTouchDetector.touchState.value.isHolding
        }
      default:
        return {}
    }
  }

  return {
    // 主要检测方法
    detectAction,
    getRequiredKeypoints,
    
    // 状态管理
    resetAllDetectors,
    resetDetector,
    getDetectorState,
    
    // 访问各个检测器（用于高级用法）
    shoulderTouchDetector,
    armRaiseDetector,
    palmFlipDetector,
    fingerTouchDetector
  }
}
