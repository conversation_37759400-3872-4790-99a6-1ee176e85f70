/**
 * 简化检测器测试文件
 * 用于验证简化检测逻辑的功能
 */
import { useSimplifiedActionDetector } from './simplifiedActionDetector'

// 模拟归一化关键点数据
const createMockKeypoints = () => {
  const keypoints = new Array(133).fill(null).map(() => [0.5, 0.5, 0.8])
  
  // 设置基本身体关键点
  keypoints[0] = [0.5, 0.3, 0.9]   // NOSE
  keypoints[5] = [0.4, 0.4, 0.9]   // LEFT_SHOULDER
  keypoints[6] = [0.6, 0.4, 0.9]   // RIGHT_SHOULDER
  keypoints[7] = [0.3, 0.5, 0.9]   // LEFT_ELBOW
  keypoints[8] = [0.7, 0.5, 0.9]   // RIGHT_ELBOW
  keypoints[9] = [0.2, 0.6, 0.9]   // LEFT_WRIST
  keypoints[10] = [0.8, 0.6, 0.9]  // RIGHT_WRIST
  
  // 设置手部关键点
  keypoints[95] = [0.15, 0.65, 0.8]  // LEFT_HAND_THUMB_4
  keypoints[99] = [0.18, 0.62, 0.8]  // LEFT_HAND_INDEX_4
  keypoints[103] = [0.19, 0.61, 0.8] // LEFT_HAND_MIDDLE_4
  keypoints[107] = [0.18, 0.63, 0.8] // LEFT_HAND_RING_4
  keypoints[111] = [0.17, 0.64, 0.8] // LEFT_HAND_PINKY_4
  
  keypoints[116] = [0.85, 0.65, 0.8] // RIGHT_HAND_THUMB_4
  keypoints[120] = [0.82, 0.62, 0.8] // RIGHT_HAND_INDEX_4
  keypoints[124] = [0.81, 0.61, 0.8] // RIGHT_HAND_MIDDLE_4
  keypoints[128] = [0.82, 0.63, 0.8] // RIGHT_HAND_RING_4
  keypoints[132] = [0.83, 0.64, 0.8] // RIGHT_HAND_PINKY_4
  
  return keypoints
}

// 测试函数
export function testSimplifiedDetectors() {
  console.log('=== 开始测试简化检测器 ===')
  
  const detector = useSimplifiedActionDetector()
  const mockKeypoints = createMockKeypoints()
  
  // 测试1: 摸肩膀检测
  console.log('\n--- 测试摸肩膀检测 ---')
  
  // 正常位置（未触摸）
  let result = detector.detectAction('shoulder_touch', mockKeypoints, 'left')
  console.log('正常位置:', result)
  
  // 模拟左手接近右肩
  const touchKeypoints = [...mockKeypoints]
  touchKeypoints[9] = [0.55, 0.42, 0.9] // 左手腕接近右肩
  result = detector.detectAction('shoulder_touch', touchKeypoints, 'left')
  console.log('接近右肩:', result)
  
  // 模拟触摸成功
  touchKeypoints[9] = [0.6, 0.4, 0.9] // 左手腕触摸右肩
  result = detector.detectAction('shoulder_touch', touchKeypoints, 'left')
  console.log('触摸成功:', result)
  
  // 测试2: 举手臂检测
  console.log('\n--- 测试举手臂检测 ---')
  
  // 正常位置（手臂放下）
  result = detector.detectAction('arm_raise', mockKeypoints, 'left')
  console.log('手臂放下:', result)
  
  // 模拟举起手臂
  const raiseKeypoints = [...mockKeypoints]
  raiseKeypoints[7] = [0.3, 0.35, 0.9] // 左肘抬高
  raiseKeypoints[9] = [0.2, 0.25, 0.9] // 左手腕抬高
  result = detector.detectAction('arm_raise', raiseKeypoints, 'left')
  console.log('手臂举起:', result)
  
  // 测试3: 手掌翻转检测
  console.log('\n--- 测试手掌翻转检测 ---')
  
  // 重置检测器状态
  detector.resetDetector('palm_flip')
  
  // 初始位置
  result = detector.detectAction('palm_flip', mockKeypoints, 'left')
  console.log('初始位置:', result)
  
  // 模拟翻转动作
  const flipKeypoints = [...mockKeypoints]
  flipKeypoints[95] = [0.25, 0.65, 0.8] // 大拇指向右移动
  result = detector.detectAction('palm_flip', flipKeypoints, 'left')
  console.log('翻转中:', result)
  
  // 测试4: 触指检测
  console.log('\n--- 测试触指检测 ---')
  
  // 重置检测器状态
  detector.resetDetector('finger_touch')
  
  // 正常位置
  result = detector.detectAction('finger_touch', mockKeypoints, 'left')
  console.log('正常位置:', result)
  
  // 模拟大拇指触摸食指
  const fingerKeypoints = [...mockKeypoints]
  fingerKeypoints[95] = [0.18, 0.62, 0.8] // 大拇指接近食指
  result = detector.detectAction('finger_touch', fingerKeypoints, 'left')
  console.log('触摸食指:', result)
  
  console.log('\n=== 测试完成 ===')
  
  return {
    detector,
    mockKeypoints,
    testResults: 'All tests completed'
  }
}

// 性能测试
export function performanceTest() {
  console.log('=== 开始性能测试 ===')
  
  const detector = useSimplifiedActionDetector()
  const mockKeypoints = createMockKeypoints()
  
  const iterations = 1000
  const startTime = performance.now()
  
  for (let i = 0; i < iterations; i++) {
    detector.detectAction('shoulder_touch', mockKeypoints, 'left')
    detector.detectAction('arm_raise', mockKeypoints, 'left')
    detector.detectAction('palm_flip', mockKeypoints, 'left')
    detector.detectAction('finger_touch', mockKeypoints, 'left')
  }
  
  const endTime = performance.now()
  const totalTime = endTime - startTime
  const avgTime = totalTime / (iterations * 4)
  
  console.log(`性能测试结果:`)
  console.log(`总迭代次数: ${iterations * 4}`)
  console.log(`总耗时: ${totalTime.toFixed(2)}ms`)
  console.log(`平均每次检测耗时: ${avgTime.toFixed(4)}ms`)
  console.log(`每秒可执行检测次数: ${(1000 / avgTime).toFixed(0)}`)
  
  console.log('=== 性能测试完成 ===')
  
  return {
    iterations: iterations * 4,
    totalTime,
    avgTime,
    detectionsPerSecond: 1000 / avgTime
  }
}

// 在浏览器控制台中可以调用的测试函数
if (typeof window !== 'undefined') {
  window.testSimplifiedDetectors = testSimplifiedDetectors
  window.performanceTest = performanceTest
}
