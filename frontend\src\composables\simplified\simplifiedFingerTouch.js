/**
 * 简化触指检测器
 * 判断大拇指是否依次与其他几个手指完成了触摸
 */
import { ref } from 'vue'
import { useSimplifiedActionEngine } from './simplifiedActionEngine'

export function useSimplifiedFingerTouch() {
  const engine = useSimplifiedActionEngine()
  const { KeyPointMapping } = engine

  // 触摸距离阈值
  const TOUCH_THRESHOLD = 0.05
  const TOUCH_HOLD_TIME = 500  // 触摸保持时间（毫秒）

  // 触摸状态记录
  const touchState = ref({
    currentTarget: 0,  // 当前目标手指索引 (0:食指, 1:中指, 2:无名指, 3:小指)
    touchStartTime: null,
    completedTouches: [],
    isHolding: false
  })

  // 手指名称映射
  const FINGER_NAMES = ['食指', '中指', '无名指', '小指']

  /**
   * 检测触指动作
   * @param {Array} keypoints - 归一化关键点数组 [x, y, confidence]
   * @param {string} side - 动作侧别 ('left' | 'right')
   * @returns {Object} - 检测结果 {isCompleted, progress, feedback}
   */
  const detectFingerTouch = (keypoints, side = 'left') => {
    // 确定关键点索引
    const thumbIndex = side === 'left' ? KeyPointMapping.LEFT_HAND_THUMB_4 : KeyPointMapping.RIGHT_HAND_THUMB_4
    const fingerIndices = side === 'left' ? [
      KeyPointMapping.LEFT_HAND_INDEX_4,   // 食指
      KeyPointMapping.LEFT_HAND_MIDDLE_4,  // 中指
      KeyPointMapping.LEFT_HAND_RING_4,    // 无名指
      KeyPointMapping.LEFT_HAND_PINKY_4    // 小指
    ] : [
      KeyPointMapping.RIGHT_HAND_INDEX_4,
      KeyPointMapping.RIGHT_HAND_MIDDLE_4,
      KeyPointMapping.RIGHT_HAND_RING_4,
      KeyPointMapping.RIGHT_HAND_PINKY_4
    ]

    // 检查大拇指是否可用
    if (!engine.isValidKeypoint(keypoints[thumbIndex])) {
      return {
        isCompleted: false,
        progress: 0,
        feedback: '请保持手部在画面内'
      }
    }

    // 如果已完成所有触摸
    if (touchState.value.completedTouches.length >= 4) {
      return {
        isCompleted: true,
        progress: 100,
        feedback: `${side === 'left' ? '左手' : '右手'}触指动作完成！`
      }
    }

    // 获取当前目标手指
    const currentTargetIndex = fingerIndices[touchState.value.currentTarget]
    const targetFingerName = FINGER_NAMES[touchState.value.currentTarget]

    // 检查当前目标手指是否可用
    if (!engine.isValidKeypoint(keypoints[currentTargetIndex])) {
      return {
        isCompleted: false,
        progress: (touchState.value.completedTouches.length / 4) * 100,
        feedback: `请保持${targetFingerName}在画面内`
      }
    }

    // 计算大拇指与当前目标手指的距离
    const thumb = keypoints[thumbIndex]
    const targetFinger = keypoints[currentTargetIndex]
    const distance = engine.calculateDistance(thumb, targetFinger)

    if (distance === Infinity) {
      return {
        isCompleted: false,
        progress: (touchState.value.completedTouches.length / 4) * 100,
        feedback: '无法检测手指位置'
      }
    }

    const now = Date.now()

    // 检查是否触摸
    if (distance <= TOUCH_THRESHOLD) {
      if (!touchState.value.isHolding) {
        // 开始触摸
        touchState.value.isHolding = true
        touchState.value.touchStartTime = now
      } else {
        // 检查是否保持足够时间
        const holdTime = now - touchState.value.touchStartTime
        if (holdTime >= TOUCH_HOLD_TIME) {
          // 完成当前手指的触摸
          touchState.value.completedTouches.push(touchState.value.currentTarget)
          touchState.value.currentTarget++
          touchState.value.isHolding = false
          touchState.value.touchStartTime = null
          
          console.log(`[FingerTouch] 完成${targetFingerName}触摸 ${touchState.value.completedTouches.length}/4`)
          
          // 检查是否全部完成
          if (touchState.value.completedTouches.length >= 4) {
            return {
              isCompleted: true,
              progress: 100,
              feedback: `${side === 'left' ? '左手' : '右手'}触指动作完成！`
            }
          }
        }
      }
    } else {
      // 没有触摸，重置触摸状态
      touchState.value.isHolding = false
      touchState.value.touchStartTime = null
    }

    // 计算进度
    const completedProgress = (touchState.value.completedTouches.length / 4) * 80
    const currentProgress = touchState.value.isHolding ? 
      Math.min(20, ((now - touchState.value.touchStartTime) / TOUCH_HOLD_TIME) * 20) : 0
    const progress = Math.round(completedProgress + currentProgress)

    // 生成反馈
    let feedback = ''
    if (touchState.value.isHolding) {
      const holdTime = now - touchState.value.touchStartTime
      const remainingTime = Math.max(0, TOUCH_HOLD_TIME - holdTime)
      feedback = `保持大拇指与${targetFingerName}接触 ${Math.ceil(remainingTime / 100) / 10}秒`
    } else {
      feedback = `请用大拇指触摸${targetFingerName} (${touchState.value.completedTouches.length + 1}/4)`
    }

    return {
      isCompleted: false,
      progress: Math.max(0, Math.min(99, progress)),
      feedback
    }
  }

  /**
   * 重置触摸状态
   */
  const resetTouchState = () => {
    touchState.value = {
      currentTarget: 0,
      touchStartTime: null,
      completedTouches: [],
      isHolding: false
    }
  }

  /**
   * 获取所需关键点索引
   * @param {string} side - 动作侧别
   * @returns {Array} - 关键点索引数组
   */
  const getRequiredKeypoints = (side) => {
    const thumbIndex = side === 'left' ? KeyPointMapping.LEFT_HAND_THUMB_4 : KeyPointMapping.RIGHT_HAND_THUMB_4
    const fingerIndices = side === 'left' ? [
      KeyPointMapping.LEFT_HAND_INDEX_4,
      KeyPointMapping.LEFT_HAND_MIDDLE_4,
      KeyPointMapping.LEFT_HAND_RING_4,
      KeyPointMapping.LEFT_HAND_PINKY_4
    ] : [
      KeyPointMapping.RIGHT_HAND_INDEX_4,
      KeyPointMapping.RIGHT_HAND_MIDDLE_4,
      KeyPointMapping.RIGHT_HAND_RING_4,
      KeyPointMapping.RIGHT_HAND_PINKY_4
    ]
    return [thumbIndex, ...fingerIndices]
  }

  return {
    detectFingerTouch,
    resetTouchState,
    getRequiredKeypoints,
    touchState,
    TOUCH_THRESHOLD,
    TOUCH_HOLD_TIME,
    FINGER_NAMES
  }
}
