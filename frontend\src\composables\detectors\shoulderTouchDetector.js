/**
 * 肩部触摸动作检测器 - 归一化坐标版
 * 使用归一化坐标进行肩部触摸检测，简化距离计算逻辑
 */
import { ref } from 'vue'
import { KeyPointMapping } from '@/utils/poseConstants'
import { useActionDetectionEngine } from '../useActionDetectionEngine'

export function useShoulderTouchDetector() {
  const engine = useActionDetectionEngine()

  // 简化的动作状态
  const actionState = ref({
    stage: 'waiting',        // waiting, approaching, touching, completed
    minDistance: null,       // 记录最小归一化距离
    hasReturned: false,      // 是否已返回
    progressScore: 0,        // 当前得分
    startTime: null          // 开始时间
  })

  // 关键点配置
  const REQUIRED_KEYPOINTS = {
    left: [KeyPointMapping.LEFT_WRIST, KeyPointMapping.RIGHT_SHOULDER],
    right: [KeyPointMapping.RIGHT_WRIST, KeyPointMapping.LEFT_SHOULDER]
  }

  /**
   * 计算手心位置（基于归一化坐标的手腕和手掌关键点）
   */
  const calculateHandCenter = (keypoints, side) => {
    const isLeft = side === 'left'

    // 获取手腕位置
    const wrist = keypoints[isLeft ? KeyPointMapping.LEFT_WRIST : KeyPointMapping.RIGHT_WRIST]

    // 获取手掌关键点（指根位置）
    const handRoot = keypoints[isLeft ? KeyPointMapping.LEFT_HAND_WRIST : KeyPointMapping.RIGHT_HAND_WRIST]
    const thumbRoot = keypoints[isLeft ? KeyPointMapping.LEFT_HAND_THUMB_1 : KeyPointMapping.RIGHT_HAND_THUMB_1]
    const indexRoot = keypoints[isLeft ? KeyPointMapping.LEFT_HAND_INDEX_1 : KeyPointMapping.RIGHT_HAND_INDEX_1]
    const middleRoot = keypoints[isLeft ? KeyPointMapping.LEFT_HAND_MIDDLE_1 : KeyPointMapping.RIGHT_HAND_MIDDLE_1]
    const ringRoot = keypoints[isLeft ? KeyPointMapping.LEFT_HAND_RING_1 : KeyPointMapping.RIGHT_HAND_RING_1]
    const pinkyRoot = keypoints[isLeft ? KeyPointMapping.LEFT_HAND_PINKY_1 : KeyPointMapping.RIGHT_HAND_PINKY_1]

    // 检查关键点是否可用（归一化坐标验证）
    const validPoints = [wrist, handRoot, thumbRoot, indexRoot, middleRoot, ringRoot, pinkyRoot]
      .filter(point => engine.isValidKeypoint(point))

    if (validPoints.length < 2) {
      return wrist // 如果手部关键点不足，回退到手腕位置
    }

    // 计算手心中心位置（所有可用点的平均值）
    // 支持数组格式 [x, y, confidence] 和对象格式 {x, y, confidence}
    const centerX = validPoints.reduce((sum, point) => {
      const x = Array.isArray(point) ? point[0] : point.x
      return sum + x
    }, 0) / validPoints.length

    const centerY = validPoints.reduce((sum, point) => {
      const y = Array.isArray(point) ? point[1] : point.y
      return sum + y
    }, 0) / validPoints.length

    return { x: centerX, y: centerY }
  }

  // 难度等级配置 - 基于归一化距离（调整为更合理的阈值）
  const DIFFICULTY_CONFIG = {
    easy: {
      touchThreshold: 0.15,      // 触摸距离阈值（归一化距离）- 降低
      approachThreshold: 0.15,   // 接近距离阈值 - 降低
      perfectThreshold: 0.05,    // 完美触摸阈值 - 降低
      returnThreshold: 0.20,     // 归位阈值 - 降低
      minCompletionScore: 60,    // 最低完成得分
      maxScore: 100,             // 最高得分
      stageTimeouts: {           // 各阶段超时时间（秒）
        approaching: 15,
        peak: 10,
        returning: 15
      }
    },
    medium: {
      touchThreshold: 0.12,
      approachThreshold: 0.20,
      perfectThreshold: 0.06,
      returnThreshold: 0.30,
      minCompletionScore: 70,
      maxScore: 100,
      stageTimeouts: {
        approaching: 12,
        peak: 8,
        returning: 12
      }
    },
    hard: {
      touchThreshold: 0.10,
      approachThreshold: 0.18,
      perfectThreshold: 0.05,
      returnThreshold: 0.25,
      minCompletionScore: 80,
      maxScore: 100,
      stageTimeouts: {
        approaching: 10,
        peak: 6,
        returning: 10
      }
    }
  }

  /**
   * 检测肩部触摸动作 - 基于归一化坐标的简化检测
   * @param {Array} keypoints - 归一化关键点数组
   * @param {string} side - 动作侧面 ('left' | 'right')
   * @param {string} difficultyLevel - 难度等级
   * @returns {Object} - 检测结果
   */
  const detectShoulderTouch = (keypoints, side = 'left', difficultyLevel = 'easy') => {
    const config = DIFFICULTY_CONFIG[difficultyLevel]
    const requiredPoints = REQUIRED_KEYPOINTS[side]

    // 检查关键点完整性（使用归一化坐标）
    const completenessCheck = engine.checkRequiredKeypoints(keypoints, requiredPoints, side)
    if (!completenessCheck.isComplete) {
      return createInvalidResult('incomplete', completenessCheck.message)
    }

    // 计算手心位置（归一化坐标）
    const handCenter = calculateHandCenter(keypoints, side)
    const targetShoulder = side === 'left' ?
      keypoints[KeyPointMapping.RIGHT_SHOULDER] :
      keypoints[KeyPointMapping.LEFT_SHOULDER]

    // 计算手心到肩膀的归一化距离
    const normalizedDistance = engine.calculateDistance(handCenter, targetShoulder)

    if (normalizedDistance === Infinity) {
      return createInvalidResult('invalid', '无法计算动作距离')
    }

    // 使用归一化距离进行检测
    const result = processNormalizedDetection(normalizedDistance, config, side)

    console.log(`[ShoulderTouch] 归一化检测:`, {
      side,
      handCenter: handCenter ? { x: handCenter.x?.toFixed(3), y: handCenter.y?.toFixed(3) } : null,
      targetShoulder: targetShoulder ? {
        x: (Array.isArray(targetShoulder) ? targetShoulder[0] : targetShoulder.x)?.toFixed(3),
        y: (Array.isArray(targetShoulder) ? targetShoulder[1] : targetShoulder.y)?.toFixed(3)
      } : null,
      normalizedDistance: normalizedDistance?.toFixed(3),
      stage: actionState.value.stage,
      progressScore: actionState.value.progressScore,
      touchThreshold: config.touchThreshold
    })

    return {
      ...result,
      distance: normalizedDistance,
      normalizedDistance,
      actionState: { ...actionState.value }
    }
  }

  /**
   * 创建无效结果对象
   */
  const createInvalidResult = (stage, feedback) => {
    return {
      accuracy: 0,
      stage,
      feedback,
      distance: Infinity,
      normalizedDistance: Infinity,
      progressScore: 0,
      actionStage: actionState.value.stage,
      isCompleted: false
    }
  }

  /**
   * 基于归一化距离的检测处理逻辑
   */
  const processNormalizedDetection = (normalizedDistance, config, side) => {
    const sideText = side === 'left' ? '左手' : '右手'
    const targetText = side === 'left' ? '右肩' : '左肩'

    // 使用配置中的归一化距离阈值
    const touchThreshold = config.touchThreshold
    const approachThreshold = config.approachThreshold

    // 初始化
    if (actionState.value.startTime === null) {
      actionState.value.startTime = Date.now()
    }

    switch (actionState.value.stage) {
      case 'waiting':
        // 检查是否进入接近阶段
        if (normalizedDistance <= approachThreshold) {
          actionState.value.stage = 'approaching'
          actionState.value.progressScore = 30
          
          return {
            accuracy: 30,
            stage: 'approaching',
            feedback: `很好！${sideText}开始接近${targetText}`,
            progressScore: 30,
            actionStage: 'approaching',
            isCompleted: false
          }
        }

        return {
          accuracy: 0,
          stage: 'waiting',
          feedback: `请将${sideText}向${targetText}移动`,
          progressScore: 0,
          actionStage: 'waiting',
          isCompleted: false
        }

      case 'approaching':
        // 检查是否达到触摸距离
        if (normalizedDistance <= touchThreshold) {
          actionState.value.stage = 'touching'
          actionState.value.minDistance = normalizedDistance
          actionState.value.progressScore = 70

          return {
            accuracy: 70,
            stage: 'touching',
            feedback: `很好！${sideText}已接触${targetText}`,
            progressScore: 70,
            actionStage: 'touching',
            isCompleted: false
          }
        }

        // 接近阶段的渐进评分
        const approachProgress = Math.max(30, Math.min(60, 
          30 + 30 * (1 - normalizedDistance / approachThreshold)))
        actionState.value.progressScore = approachProgress

        return {
          accuracy: approachProgress,
          stage: 'approaching',
          feedback: `继续将${sideText}向${targetText}移动`,
          progressScore: approachProgress,
          actionStage: 'approaching',
          isCompleted: false
        }

      case 'touching':
        // 更新最小距离
        if (normalizedDistance < actionState.value.minDistance) {
          actionState.value.minDistance = normalizedDistance
        }

        // 检测是否开始返回（距离增加超过阈值）
        const returnThreshold = config.returnThreshold
        if (normalizedDistance > returnThreshold) {
          actionState.value.stage = 'completed'
          actionState.value.hasReturned = true
          actionState.value.progressScore = 100

          return {
            accuracy: 100,
            stage: 'completed',
            feedback: `完美！${sideText}触摸${targetText}动作完成`,
            progressScore: 100,
            actionStage: 'completed',
            isCompleted: true
          }
        }

        // 继续触摸阶段 - 距离越近分数越高
        const perfectThreshold = config.perfectThreshold
        const touchScore = Math.min(95, 70 + Math.max(0, 
          25 * (1 - normalizedDistance / perfectThreshold)))
        actionState.value.progressScore = touchScore

        return {
          accuracy: touchScore,
          stage: 'touching',
          feedback: `保持接触，然后慢慢返回`,
          progressScore: touchScore,
          actionStage: 'touching',
          isCompleted: false
        }

      case 'completed':
        return {
          accuracy: 100,
          stage: 'completed',
          feedback: `动作已完成！可以进行下一个动作`,
          progressScore: 100,
          actionStage: 'completed',
          isCompleted: true
        }

      default:
        actionState.value.stage = 'waiting'
        return {
          accuracy: 0,
          stage: 'waiting',
          feedback: `请将${sideText}向${targetText}移动`,
          progressScore: 0,
          actionStage: 'waiting',
          isCompleted: false
        }
    }
  }

  /**
   * 重置检测器状态
   */
  const resetDetector = () => {
    actionState.value = {
      stage: 'waiting',
      minDistance: null,
      hasReturned: false,
      progressScore: 0,
      startTime: null
    }
  }

  /**
   * 检查动作是否完成
   */
  const checkActionCompleted = () => {
    return actionState.value.stage === 'completed' && actionState.value.hasReturned
  }

  /**
   * 检查动作是否完成（简化版）
   */
  const isActionCompleted = () => {
    return actionState.value.stage === 'completed'
  }

  return {
    // 主要检测方法
    detectShoulderTouch,
    isActionCompleted,
    checkActionCompleted,

    // 状态管理
    actionState,
    resetDetector,

    // 配置
    REQUIRED_KEYPOINTS,
    DIFFICULTY_CONFIG
  }
}
