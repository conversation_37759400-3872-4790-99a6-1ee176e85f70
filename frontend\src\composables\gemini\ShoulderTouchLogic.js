import { KeyPointMapping } from "@/utils/poseConstants";
import { utils } from "@/utils/geometryUtils";

class ShoulderTouchLogic {
    constructor(side = 'left', level = 'medium') {
        this.side = side;
        this.level = level;

        // 动态设置关键点索引 (同前)
        if (this.side === 'left') {
            this.movingWristIdx = 9; // LEFT_WRIST
            this.movingShoulderIdx = 5; // LEFT_SHOULDER
            this.targetShoulderIdx = 6; // RIGHT_SHOULDER
        } else {
            this.movingWristIdx = 10; // RIGHT_WRIST
            this.movingShoulderIdx = 6; // RIGHT_SHOULDER
            this.targetShoulderIdx = 5; // LEFT_SHOULDER
        }

        // 根据难度设置参数 (同前)
        this.config = this.getDifficultyConfig(level);

        // 初始化或重置内部状态
        this.reset();
    }

    getDifficultyConfig(level) {
        switch (level) {
            case 'easy':
                return { holdDuration: 1000, distanceThresholdFactor: 0.20 };
            case 'hard':
                return { holdDuration: 3000, distanceThresholdFactor: 0.10 };
            case 'medium':
            default:
                return { holdDuration: 2000, distanceThresholdFactor: 0.15 };
        }
    }

    reset() {
        this.state = 'IDLE';
        this.score = 0;
        this.feedback = `请准备，将${this.side === 'left' ? '左' : '右'}手掌移动到对侧肩膀。`;

        // 内部状态变量
        this.holdStartTime = 0;
        this.initialDistance = 0;
        this.shoulderWidth = 0;
    }

    /**
     * 核心评估方法
     * @param {Array<[number, number, number]>} keypoints - 实时关键点数据
     * @returns {{state: string, score: number, feedback: string}} - 评估结果
     */
    update(keypoints) {
        // 如果动作已完成，则不再更新
        if (this.state === 'COMPLETED') {
            return { state: this.state, score: this.score, feedback: this.feedback };
        }

        // --- 数据提取与前置检查 ---
        const getPoint = (idx) => ({ x: keypoints[idx][0], y: keypoints[idx][1], c: keypoints[idx][2] });
        const movingWrist = getPoint(this.movingWristIdx);
        const movingShoulder = getPoint(this.movingShoulderIdx);
        const targetShoulder = getPoint(this.targetShoulderIdx);

        const confidenceThreshold = 0.5;
        if (movingWrist.c < confidenceThreshold || movingShoulder.c < confidenceThreshold || targetShoulder.c < confidenceThreshold) {
            return { state: this.state, score: this.score, feedback: '请保证关键身体部位在画面中' };
        }

        // --- 几何计算 ---
        const currentDistance = utils.calculateDistance(movingWrist, targetShoulder);
        if (this.shoulderWidth === 0) { // 只计算一次肩宽
            this.shoulderWidth = utils.calculateDistance(movingShoulder, targetShoulder);
        }
        const touchThreshold = this.shoulderWidth * this.config.distanceThresholdFactor;

        // --- 状态机与动态计分逻辑 ---
        switch (this.state) {
            case 'IDLE':
                // 等待用户开始动作
                // 条件: 手腕开始向身体中线移动
                const isStarting = (this.side === 'left' && movingWrist.x > movingShoulder.x) || 
                                   (this.side === 'right' && movingWrist.x < movingShoulder.x);
                if (isStarting) {
                    this.state = 'MOVING_TO_TARGET';
                    this.initialDistance = currentDistance; // 捕获起始距离
                    this.feedback = '很好，正在向目标移动...';
                }
                break;

            case 'MOVING_TO_TARGET':
                // 分数范围: 0 -> 50
                // 进度 = 1 - (当前距离 / 初始距离)
                const progress = 1 - (currentDistance / this.initialDistance);
                this.score = Math.max(0, Math.min(50, 50 * progress));
                this.feedback = '继续移动...';

                // 条件: 到达目标
                if (currentDistance < touchThreshold) {
                    this.state = 'HOLDING';
                    this.holdStartTime = Date.now();
                    this.score = 50; // 触碰成功，基础分50
                    this.feedback = `太棒了，请保持 ${this.config.holdDuration / 1000} 秒`;
                }
                break;

            case 'HOLDING':
                // 分数范围: 50 -> 80
                const holdingTime = Date.now() - this.holdStartTime;
                
                // 条件1: 手在保持期间移开，则退回上一个状态
                if (currentDistance > touchThreshold * 1.2) { // 使用1.2倍阈值增加容错
                    this.state = 'MOVING_TO_TARGET';
                    this.feedback = '请将手放回肩膀上并保持稳定';
                    // 分数会根据上面的MOVING_TO_TARGET逻辑自动回落，形成惩罚
                    break;
                }

                // 根据保持时间进度计算分数
                const holdProgress = Math.min(1, holdingTime / this.config.holdDuration);
                this.score = 50 + 30 * holdProgress;
                
                // 条件2: 保持时间足够
                if (holdProgress >= 1) {
                    this.state = 'RETURNING';
                    this.score = 80; // 保持成功，分数达到80
                    this.feedback = '很好！现在将手掌返回起始位置';
                } else {
                    this.feedback = `保持... 剩余 ${Math.ceil((this.config.holdDuration - holdingTime) / 1000)} 秒`;
                }
                break;

            case 'RETURNING':
                // 分数范围: 80 -> 100
                // 返回进度 = (当前距离 / 初始距离)，这个值从一个很小的值增长到1
                const returnProgress = Math.min(1, currentDistance / this.initialDistance);
                this.score = 80 + 20 * returnProgress;
                this.feedback = '正在返回起始位置...';
                
                // 条件: 手腕回到身体同侧，认为返回完成
                const isBackToStart = (this.side === 'left' && movingWrist.x < movingShoulder.x - this.shoulderWidth * 0.1) || 
                                      (this.side === 'right' && movingWrist.x > movingShoulder.x + this.shoulderWidth * 0.1);
                                      // 增加一点缓冲，防止在肩膀边缘就判断完成
                if (isBackToStart) {
                    this.state = 'COMPLETED';
                    this.score = 100; // 完美返回，给满分
                    this.feedback = `动作完成！最终得分: ${Math.round(this.score)}`;
                }
                break;
        }

        // 返回实时评估结果，分数取整
        return {
            state: this.state,
            score: Math.round(this.score),
            feedback: this.feedback
        };
    }
}

export default ShoulderTouchLogic;